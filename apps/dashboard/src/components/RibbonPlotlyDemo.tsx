import React from 'react';
import RibbonPlotly from '../widgets/3d-ribbon/3d-ribbon-plotly';

export const RibbonPlotlyDemo: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-950 text-white">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            3D Ribbon Chart
          </h1>
          <p className="text-gray-400 text-lg">
            Interactive 3D ribbon plot with comprehensive controls using react-plotly.js.
            Features real-time parameter adjustments, camera controls, and responsive design.
          </p>
        </div>

        <div className="bg-gray-900 rounded-lg p-6">
          <RibbonPlotly />
        </div>

        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Features</h2>
          <ul className="text-gray-300 space-y-2">
            <li>• Interactive 3D surface plots with multiple ribbons</li>
            <li>• Real-time parameter controls (colorscale, opacity, lighting)</li>
            <li>• Rotation limiting with configurable angle constraints</li>
            <li>• Camera reset functionality</li>
            <li>• Responsive design with loading states</li>
            <li>• Built with react-plotly.js for reliable 3D rendering</li>
          </ul>
        </div>
      </div>
    </div>
  );
};
