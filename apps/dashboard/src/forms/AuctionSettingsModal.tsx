import { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { AuctionSettings } from './AuctionSettings';
import { createDefaultDeSettingsValue, createEmptyDeSettingsValue } from './AuctionSettings.helpers';
import type { DeSettingsValue, DeAuctionSaveCommand } from '@/api-client';
import { Crud } from '@/api-client';

export interface AuctionSettingsModalProps {
  /** Whether the modal is open */
  open: boolean;
  /** Function to close the modal */
  onClose: () => void;
  /** Initial settings to display/edit */
  initialSettings?: DeSettingsValue;
  /** CRUD mode - determines available actions */
  mode: 'create' | 'view' | 'edit';
  /** Whether to show debug buttons (Clear/Default) */
  showDebugButtons?: boolean;
  /** Callback when auction is saved */
  onSaveAuction?: (command: DeAuctionSaveCommand) => void;
  /** Callback when settings are saved as template */
  onSaveAsTemplate?: () => void;
}

// Helper function to convert DeSettingsValue to DeAuctionSaveCommand
function convertToSaveCommand(settings: DeSettingsValue, auctionId: string): DeAuctionSaveCommand {
  const startingTime = settings.starting_time;

  return {
    auction_id: auctionId,
    auction_name: settings.auction_name,
    cost_multiplier: settings.cost_multiplier,
    excess_level_0_label: settings.excess_level_0_label,
    excess_level_1_label: settings.excess_level_1_label,
    excess_level_1_quantity: settings.excess_level_1_quantity,
    excess_level_2_label: settings.excess_level_2_label,
    excess_level_2_quantity: settings.excess_level_2_quantity,
    excess_level_3_label: settings.excess_level_3_label,
    excess_level_3_quantity: settings.excess_level_3_quantity,
    excess_level_4_label: settings.excess_level_4_label,
    excess_level_4_quantity: settings.excess_level_4_quantity,
    month_is_1_based: true, // Standard setting
    price_change_initial: settings.price_change_initial,
    price_change_post_reversal: settings.price_change_post_reversal,
    price_decimal_places: settings.price_decimal_places.toString(),
    price_label: settings.price_label,
    quantity_label: settings.quantity_label,
    quantity_minimum: settings.quantity_minimum,
    quantity_step: settings.quantity_step,
    round_closed_min_secs: settings.round_closed_min_secs.toString(),
    round_open_min_seconds: settings.round_open_min_secs.toString(), // Note: different field name
    round_orange_secs: settings.round_orange_secs.toString(),
    round_red_secs: settings.round_red_secs.toString(),
    starting_day: startingTime?.day_of_month.toString() || '1',
    starting_hour: startingTime?.hour.toString() || '0',
    starting_mins: startingTime?.minutes.toString() || '0',
    starting_month: startingTime ? (startingTime.month + 1).toString() : '1', // Convert 0-based to 1-based
    starting_price_announcement_mins: settings.starting_price_announcement_mins.toString(),
    starting_year: startingTime?.year.toString() || new Date().getFullYear().toString(),
    use_counterparty_credits: settings.use_counterparty_credits.toString(),
  };
}

export function AuctionSettingsModal({
  open,
  onClose,
  initialSettings,
  mode,
  showDebugButtons = false,
  onSaveAuction,
  onSaveAsTemplate,
}: AuctionSettingsModalProps) {
  const [isEditing, setIsEditing] = useState(mode === 'create' || mode === 'edit');
  const [settings, setSettings] = useState<DeSettingsValue>(
    initialSettings || createDefaultDeSettingsValue()
  );

  // Reset state when modal opens/closes or initial settings change
  useEffect(() => {
    if (open) {
      setSettings(initialSettings || createDefaultDeSettingsValue());
      setIsEditing(mode === 'create' || mode === 'edit');
    }
  }, [open, initialSettings, mode]);

  // Determine CRUD mode for the form
  const getCrudMode = (): Crud => {
    if (!isEditing) return Crud.READ;
    return mode === 'create' ? Crud.CREATE : Crud.UPDATE;
  };

  const handleSave = () => {
    if (onSaveAuction) {
      // Generate auction ID for new auctions (in real app, this would come from server)
      const auctionId = mode === 'create' ? `auction_${Date.now()}` : initialSettings?.auction_name || 'unknown';
      const command = convertToSaveCommand(settings, auctionId);
      onSaveAuction(command);
    }
    onClose();
  };

  const handleEdit = () => {
    setIsEditing(true);
  };

  const handleClear = () => {
    setSettings(createEmptyDeSettingsValue());
  };

  const handleDefault = () => {
    setSettings(createDefaultDeSettingsValue());
  };

  const handleSaveAsTemplate = () => {
    if (onSaveAsTemplate) {
      onSaveAsTemplate();
    }
  };

  const getTitle = () => {
    switch (mode) {
      case 'create': return 'Create Auction';
      case 'edit': return 'Edit Auction Settings';
      case 'view': return 'Auction Settings';
      default: return 'Auction Settings';
    }
  };

  const getDescription = () => {
    switch (mode) {
      case 'create': return 'Configure settings for a new auction';
      case 'edit': return 'Modify auction settings';
      case 'view': return 'View auction configuration';
      default: return '';
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-[600px] max-h-[90vh] overflow-hidden">
        <DialogHeader>
          <DialogTitle>{getTitle()}</DialogTitle>
          <DialogDescription>{getDescription()}</DialogDescription>
        </DialogHeader>

        <div className="overflow-y-auto max-h-[60vh] pr-2">
          <AuctionSettings
            settings={settings}
            crud={getCrudMode()}
            onSettingsChange={setSettings}
          />
        </div>

        <DialogFooter className="flex-wrap gap-2">
          {/* Edit button - only show in view mode */}
          {mode === 'view' && !isEditing && (
            <Button onClick={handleEdit} variant="default">
              Edit
            </Button>
          )}

          {/* Save button - only show when editing */}
          {isEditing && (
            <Button onClick={handleSave} variant="default">
              Save
            </Button>
          )}

          {/* Debug buttons - only show when editing and debug enabled */}
          {isEditing && showDebugButtons && (
            <>
              <Button onClick={handleClear} variant="outline">
                Clear
              </Button>
              <Button onClick={handleDefault} variant="outline">
                Default
              </Button>
            </>
          )}

          {/* Save as Template button */}
          <Button onClick={handleSaveAsTemplate} variant="outline">
            Save as Template
          </Button>

          {/* Close/Cancel button */}
          <Button onClick={onClose} variant="outline">
            {isEditing ? 'Cancel' : 'Close'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
