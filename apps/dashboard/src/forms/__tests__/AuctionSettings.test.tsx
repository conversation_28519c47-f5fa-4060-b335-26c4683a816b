import { render, screen, fireEvent } from '@testing-library/react';
import { AuctionSettings } from '../AuctionSettings';
import { createDefaultDeSettingsValue } from '../AuctionSettings.helpers';
import { Crud } from '@/api-client';

describe('AuctionSettings', () => {
  const mockOnSettingsChange = jest.fn();
  const defaultSettings = createDefaultDeSettingsValue();

  beforeEach(() => {
    mockOnSettingsChange.mockClear();
  });

  it('renders auction settings form', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.READ}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    expect(screen.getByText('Auction Name')).toBeInTheDocument();
    expect(screen.getByText('Round Timer')).toBeInTheDocument();
    expect(screen.getByText('Price settings')).toBeInTheDocument();
    expect(screen.getByText('Quantity Settings')).toBeInTheDocument();
  });

  it('displays auction name correctly', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.READ}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    const auctionNameInput = screen.getByDisplayValue(defaultSettings.auction_name);
    expect(auctionNameInput).toBeInTheDocument();
  });

  it('allows editing in CREATE mode', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.CREATE}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    const auctionNameInput = screen.getByDisplayValue(defaultSettings.auction_name);
    expect(auctionNameInput).not.toBeDisabled();
  });

  it('disables editing in READ mode', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.READ}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    const auctionNameInput = screen.getByDisplayValue(defaultSettings.auction_name);
    expect(auctionNameInput).toBeDisabled();
  });

  it('calls onSettingsChange when auction name is updated', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.UPDATE}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    const auctionNameInput = screen.getByDisplayValue(defaultSettings.auction_name);
    fireEvent.change(auctionNameInput, { target: { value: 'New Auction Name' } });

    expect(mockOnSettingsChange).toHaveBeenCalledWith({
      ...defaultSettings,
      auction_name: 'New Auction Name',
    });
  });

  it('displays price settings correctly', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.READ}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    expect(screen.getByDisplayValue(defaultSettings.price_change_initial)).toBeInTheDocument();
    expect(screen.getByDisplayValue(defaultSettings.price_change_post_reversal)).toBeInTheDocument();
    expect(screen.getByDisplayValue(defaultSettings.price_label)).toBeInTheDocument();
  });

  it('displays quantity settings correctly', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.READ}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    expect(screen.getByDisplayValue(defaultSettings.quantity_label)).toBeInTheDocument();
    expect(screen.getByDisplayValue(defaultSettings.quantity_step)).toBeInTheDocument();
    expect(screen.getByDisplayValue(defaultSettings.quantity_minimum)).toBeInTheDocument();
  });

  it('displays excess levels table correctly', () => {
    render(
      <AuctionSettings
        settings={defaultSettings}
        crud={Crud.READ}
        onSettingsChange={mockOnSettingsChange}
      />
    );

    expect(screen.getByDisplayValue(defaultSettings.excess_level_1_label)).toBeInTheDocument();
    expect(screen.getByDisplayValue(defaultSettings.excess_level_1_quantity)).toBeInTheDocument();
    expect(screen.getByDisplayValue(defaultSettings.excess_level_2_label)).toBeInTheDocument();
    expect(screen.getByDisplayValue(defaultSettings.excess_level_2_quantity)).toBeInTheDocument();
  });
});
