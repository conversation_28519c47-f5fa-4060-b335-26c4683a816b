import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { AuctionSettingsModal } from '../AuctionSettingsModal';
import { createDefaultDeSettingsValue } from '../AuctionSettings.helpers';
import type { DeAuctionSaveCommand } from '@/api-client';

describe('AuctionSettingsModal', () => {
  const mockOnClose = jest.fn();
  const mockOnSaveAuction = jest.fn();
  const mockOnSaveAsTemplate = jest.fn();
  const defaultSettings = createDefaultDeSettingsValue();

  beforeEach(() => {
    mockOnClose.mockClear();
    mockOnSaveAuction.mockClear();
    mockOnSaveAsTemplate.mockClear();
  });

  it('renders modal when open', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="view"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.getByText('Auction Settings')).toBeInTheDocument();
    expect(screen.getByText('View auction configuration')).toBeInTheDocument();
  });

  it('does not render modal when closed', () => {
    render(
      <AuctionSettingsModal
        open={false}
        onClose={mockOnClose}
        mode="view"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.queryByText('Auction Settings')).not.toBeInTheDocument();
  });

  it('shows correct title and description for create mode', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="create"
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.getByText('Create Auction')).toBeInTheDocument();
    expect(screen.getByText('Configure settings for a new auction')).toBeInTheDocument();
  });

  it('shows correct title and description for edit mode', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="edit"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.getByText('Edit Auction Settings')).toBeInTheDocument();
    expect(screen.getByText('Modify auction settings')).toBeInTheDocument();
  });

  it('shows Edit button in view mode', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="view"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.getByText('Edit')).toBeInTheDocument();
    expect(screen.queryByText('Save')).not.toBeInTheDocument();
  });

  it('shows Save button in create mode', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="create"
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });

  it('shows Save button in edit mode', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="edit"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });

  it('shows debug buttons when enabled', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="create"
        showDebugButtons={true}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    expect(screen.getByText('Clear')).toBeInTheDocument();
    expect(screen.getByText('Default')).toBeInTheDocument();
  });

  it('calls onClose when close button is clicked', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="view"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    fireEvent.click(screen.getByText('Close'));
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  it('calls onSaveAsTemplate when save as template button is clicked', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="view"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    fireEvent.click(screen.getByText('Save as Template'));
    expect(mockOnSaveAsTemplate).toHaveBeenCalledTimes(1);
  });

  it('calls onSaveAuction with correct command when save is clicked', async () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="create"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    fireEvent.click(screen.getByText('Save'));

    await waitFor(() => {
      expect(mockOnSaveAuction).toHaveBeenCalledTimes(1);
    });

    const saveCommand = mockOnSaveAuction.mock.calls[0][0] as DeAuctionSaveCommand;
    expect(saveCommand.auction_name).toBe(defaultSettings.auction_name);
    expect(saveCommand.price_label).toBe(defaultSettings.price_label);
    expect(saveCommand.quantity_label).toBe(defaultSettings.quantity_label);
  });

  it('switches to edit mode when edit button is clicked', () => {
    render(
      <AuctionSettingsModal
        open={true}
        onClose={mockOnClose}
        mode="view"
        initialSettings={defaultSettings}
        onSaveAuction={mockOnSaveAuction}
        onSaveAsTemplate={mockOnSaveAsTemplate}
      />
    );

    fireEvent.click(screen.getByText('Edit'));
    
    // Should now show Save button instead of Edit
    expect(screen.getByText('Save')).toBeInTheDocument();
    expect(screen.queryByText('Edit')).not.toBeInTheDocument();
  });
});
