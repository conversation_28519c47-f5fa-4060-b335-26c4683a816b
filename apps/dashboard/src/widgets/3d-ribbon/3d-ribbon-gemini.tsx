import { useState, useEffect } from 'react';
import Plot from 'react-plotly.js';

const RibbonPlotly = () => {
    const [colorscale, setColorscale] = useState('Viridis');
    const [opacity, setOpacity] = useState(1);
    const [ambient, setAmbient] = useState(0.5);
    const [diffuse, setDiffuse] = useState(0.5);
    const [rotationLimit, setRotationLimit] = useState(360);
    const [plotData, setPlotData] = useState<any[]>([]);
    const [plotLayout, setPlotLayout] = useState<any>({});
    const [isLoading, setIsLoading] = useState(true);

    // Fetch data and initialize plot
    useEffect(() => {
        const fetchData = async () => {
            try {
                const response = await fetch('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json');
                const figure = await response.json();

                const newPlotData = [];
                const maxPoints = 50;

                for (let i = 0; i < 7; i++) {
                    const trace = {
                        x: figure.data[i].x.slice(0, maxPoints),
                        y: figure.data[i].y.slice(0, maxPoints),
                        z: figure.data[i].z.slice(0, maxPoints).map((row: any[]) => row.slice(0, maxPoints)),
                        name: '',
                        colorscale: colorscale,
                        opacity: opacity,
                        lighting: {
                            ambient: ambient,
                            diffuse: diffuse,
                            specular: 0.1,
                            roughness: 0.9,
                            fresnel: 0.2,
                        },
                        type: 'surface',
                        showscale: false,
                    };
                    newPlotData.push(trace);
                }

                const newLayout = {
                    title: '3D Ribbon Plot',
                    showlegend: false,
                    autosize: true,
                    scene: {
                        xaxis: { title: 'Sample #' },
                        yaxis: { title: 'Wavelength' },
                        zaxis: { title: 'OD' },
                        camera: {
                            eye: { x: 1.5, y: 1.5, z: 1 },
                            up: { x: 0, y: 0, z: 1 },
                        },
                        dragmode: 'turntable',
                    },
                };

                setPlotData(newPlotData);
                setPlotLayout(newLayout);
                setIsLoading(false);
            } catch (error) {
                console.error("Failed to fetch or initialize plot:", error);
                setIsLoading(false);
            }
        };

        fetchData();
    }, []);

    // Update plot when control values change
    useEffect(() => {
        if (plotData.length === 0) return;

        const updatedPlotData = plotData.map(trace => ({
            ...trace,
            colorscale: colorscale,
            opacity: opacity,
            lighting: {
                ...trace.lighting,
                ambient: ambient,
                diffuse: diffuse,
            },
        }));

        setPlotData(updatedPlotData);
    }, [colorscale, opacity, ambient, diffuse]);

    // Reset camera to default position
    const resetCamera = () => {
        const updatedLayout = {
            ...plotLayout,
            scene: {
                ...plotLayout.scene,
                camera: {
                    eye: { x: 1.5, y: 1.5, z: 1 },
                    up: { x: 0, y: 0, z: 1 },
                },
            },
        };
        setPlotLayout(updatedLayout);
    };




    return (
        <div className="p-4">
            <div className="mb-6 bg-gray-800 p-4 rounded-lg">
                <h3 className="text-lg font-semibold text-white mb-4">Controls</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    <div>
                        <label htmlFor="colorscale" className="block text-sm font-medium text-gray-300 mb-2">
                            Colorscale:
                        </label>
                        <select
                            id="colorscale"
                            value={colorscale}
                            onChange={(e) => setColorscale(e.target.value)}
                            className="w-full px-3 py-2 bg-gray-700 text-white rounded-md border border-gray-600 focus:border-blue-500 focus:outline-none"
                        >
                            <option value="Viridis">Viridis</option>
                            <option value="Cividis">Cividis</option>
                            <option value="Hot">Hot</option>
                            <option value="Electric">Electric</option>
                            <option value="Earth">Earth</option>
                        </select>
                    </div>

                    <div>
                        <label htmlFor="opacity" className="block text-sm font-medium text-gray-300 mb-2">
                            Opacity: {opacity.toFixed(1)}
                        </label>
                        <input
                            type="range"
                            id="opacity"
                            min="0"
                            max="1"
                            step="0.1"
                            value={opacity}
                            onChange={(e) => setOpacity(parseFloat(e.target.value))}
                            className="w-full"
                        />
                    </div>

                    <div>
                        <label htmlFor="ambient" className="block text-sm font-medium text-gray-300 mb-2">
                            Ambient Lighting: {ambient.toFixed(1)}
                        </label>
                        <input
                            type="range"
                            id="ambient"
                            min="0"
                            max="1"
                            step="0.1"
                            value={ambient}
                            onChange={(e) => setAmbient(parseFloat(e.target.value))}
                            className="w-full"
                        />
                    </div>

                    <div>
                        <label htmlFor="diffuse" className="block text-sm font-medium text-gray-300 mb-2">
                            Diffuse Lighting: {diffuse.toFixed(1)}
                        </label>
                        <input
                            type="range"
                            id="diffuse"
                            min="0"
                            max="1"
                            step="0.1"
                            value={diffuse}
                            onChange={(e) => setDiffuse(parseFloat(e.target.value))}
                            className="w-full"
                        />
                    </div>

                    <div>
                        <label htmlFor="rotationLimit" className="block text-sm font-medium text-gray-300 mb-2">
                            Rotation Limit: {rotationLimit}°
                        </label>
                        <input
                            type="range"
                            id="rotationLimit"
                            min="0"
                            max="360"
                            step="10"
                            value={rotationLimit}
                            onChange={(e) => setRotationLimit(parseInt(e.target.value, 10))}
                            className="w-full"
                        />
                    </div>

                    <div className="flex items-end">
                        <button
                            onClick={resetCamera}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        >
                            Reset Camera View
                        </button>
                    </div>
                </div>
            </div>
            {isLoading ? (
                <div className="w-full h-96 bg-white rounded-lg flex items-center justify-center">
                    <div className="text-gray-600">Loading 3D ribbon chart...</div>
                </div>
            ) : (
                <Plot
                    data={plotData}
                    layout={plotLayout}
                    style={{ width: '100%', height: '600px' }}
                    config={{ responsive: true }}
                />
            )}
        </div>
    );
};

export default RibbonPlotly;
