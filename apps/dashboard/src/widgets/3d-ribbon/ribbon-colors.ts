/**
 * Color schemes for 3D ribbon chart
 * Green for buy orders, red for sell orders
 */

// Green colorscale for buy orders (positive quantities)
export const buyOrderColorscale: Array<[string, string]> = [
  ['0', 'rgb(200, 255, 200)'],    // Light green
  ['0.2', 'rgb(150, 255, 150)'],  // Medium light green
  ['0.4', 'rgb(100, 255, 100)'],  // Medium green
  ['0.6', 'rgb(50, 200, 50)'],    // Medium dark green
  ['0.8', 'rgb(25, 150, 25)'],    // Dark green
  ['1', 'rgb(0, 100, 0)']         // Very dark green
];

// Red colorscale for sell orders (negative quantities)
export const sellOrderColorscale: Array<[string, string]> = [
  ['0', 'rgb(255, 200, 200)'],    // Light red
  ['0.2', 'rgb(255, 150, 150)'],  // Medium light red
  ['0.4', 'rgb(255, 100, 100)'],  // Medium red
  ['0.6', 'rgb(200, 50, 50)'],    // Medium dark red
  ['0.8', 'rgb(150, 25, 25)'],    // Dark red
  ['1', 'rgb(100, 0, 0)']         // Very dark red
];

// Alternative single-color scales for simpler appearance
export const buyOrderSingleColor: Array<[string, string]> = [
  ['0', 'rgb(34, 139, 34)'],      // Forest green
  ['1', 'rgb(34, 139, 34)']       // Forest green
];

export const sellOrderSingleColor: Array<[string, string]> = [
  ['0', 'rgb(220, 20, 60)'],      // Crimson
  ['1', 'rgb(220, 20, 60)']       // Crimson
];

// Color utility functions
export const getBuyColorscale = (singleColor = false) =>
  singleColor ? buyOrderSingleColor : buyOrderColorscale;

export const getSellColorscale = (singleColor = false) =>
  singleColor ? sellOrderSingleColor : sellOrderColorscale;
