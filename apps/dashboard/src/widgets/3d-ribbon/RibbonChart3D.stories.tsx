/**
 * Storybook stories for 3D Ribbon Chart
 */

import type { Meta, StoryObj } from '@storybook/react';
import { RibbonChart3D } from './RibbonChart3D';

const meta: Meta<typeof RibbonChart3D> = {
  title: 'Widgets/3D Ribbon Chart',
  component: RibbonChart3D,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A 3D ribbon chart using Plotly.js that displays spectral data as ribbons. Direct translation from Vue Chart8PlotlyGPTo1.vue with interactive controls for colorscale, opacity, and lighting.'
      }
    }
  },
  argTypes: {
    width: { control: { type: 'range', min: 400, max: 1200, step: 50 } },
    height: { control: { type: 'range', min: 300, max: 800, step: 50 } }
  }
};

export default meta;
type Story = StoryObj<typeof RibbonChart3D>;

// Basic story
export const Default: Story = {
  args: {
    width: 800,
    height: 600
  }
};

// Small size
export const Small: Story = {
  args: {
    width: 600,
    height: 400
  }
};

// Large size
export const Large: Story = {
  args: {
    width: 1000,
    height: 700
  }
};
