/**
 * Data transformation utilities for 3D ribbon chart
 * Converts round-table data to Plotly 3d-ribbon format (like 3d-ribbon.json)
 */

import type { DeRoundTraderElement, DeTraderElement } from '@/api-client';
import { OrderType } from '@/api-client';

export interface RibbonSurfaceData {
  type: 'surface';
  x: number[][];
  y: number[][];
  z: number[][];
  colorscale: string;
  opacity: number;
  lighting: {
    ambient: number;
    diffuse: number;
    specular: number;
    roughness: number;
    fresnel: number;
  };
  showscale: boolean;
  name?: string;
}

export interface RibbonChartData {
  surfaces: RibbonSurfaceData[];
  layout: {
    title: string;
    showlegend: boolean;
    autosize: boolean;
    scene: {
      xaxis: { title: string };
      yaxis: { title: string };
      zaxis: { title: string };
      camera: {
        eye: { x: number; y: number; z: number };
        up: { x: number; y: number; z: number };
      };
      dragmode: string;
    };
  };
}

/**
 * Transform round-trader data into 3d-ribbon format (like <PERSON><PERSON><PERSON>'s 3d-ribbon.json)
 * Creates smooth ribbon surfaces for buy and sell orders
 */
export function transformToRibbonData(
  roundTraderElements: DeRoundTraderElement[],
  _traders: DeTraderElement[],
  options: {
    opacity?: number;
    ambient?: number;
    diffuse?: number;
    maxPoints?: number;
  } = {}
): RibbonChartData {
  const {
    opacity = 0.8,
    ambient = 0.5,
    diffuse = 0.5,
    maxPoints = 50
  } = options;

  const surfaces: RibbonSurfaceData[] = [];

  // Get unique rounds and traders
  const rounds = [...new Set(roundTraderElements.map(rte => rte.round))].sort((a, b) => a - b);
  const traderIds = [...new Set(roundTraderElements.map(rte => rte.cid))];

  // Limit the number of points for performance
  const limitedRounds = rounds.slice(0, maxPoints);
  const limitedTraders = traderIds.slice(0, maxPoints);

  // Create data matrix: rounds x traders
  const buyMatrix: number[][] = [];
  const sellMatrix: number[][] = [];

  // Initialize matrices
  for (let i = 0; i < limitedRounds.length; i++) {
    buyMatrix[i] = new Array(limitedTraders.length).fill(0);
    sellMatrix[i] = new Array(limitedTraders.length).fill(0);
  }

  // Fill matrices with data
  roundTraderElements.forEach(rte => {
    const roundIndex = limitedRounds.indexOf(rte.round);
    const traderIndex = limitedTraders.indexOf(rte.cid);

    if (roundIndex >= 0 && traderIndex >= 0) {
      if (rte.order_type === OrderType.BUY) {
        buyMatrix[roundIndex][traderIndex] = rte.quantity_int;
      } else if (rte.order_type === OrderType.SELL) {
        sellMatrix[roundIndex][traderIndex] = rte.quantity_int;
      }
    }
  });

  // Create X and Y coordinate grids
  const xGrid: number[][] = [];
  const yGrid: number[][] = [];

  for (let i = 0; i < limitedRounds.length; i++) {
    xGrid[i] = limitedTraders.map(() => limitedRounds[i]);
    yGrid[i] = limitedTraders.map((_, j) => j);
  }

  // Create buy surface (positive Z values)
  if (buyMatrix.some(row => row.some(val => val > 0))) {
    const buySurface: RibbonSurfaceData = {
      type: 'surface',
      x: xGrid,
      y: yGrid,
      z: buyMatrix,
      colorscale: 'Greens',
      opacity,
      lighting: {
        ambient,
        diffuse,
        specular: 0.1,
        roughness: 0.9,
        fresnel: 0.2,
      },
      showscale: false,
      name: 'Buy Orders'
    };
    surfaces.push(buySurface);
  }

  // Create sell surface (negative Z values for visual separation)
  if (sellMatrix.some(row => row.some(val => val > 0))) {
    const sellMatrixNegative = sellMatrix.map(row => row.map(val => val > 0 ? -val : 0));

    const sellSurface: RibbonSurfaceData = {
      type: 'surface',
      x: xGrid,
      y: yGrid,
      z: sellMatrixNegative,
      colorscale: 'Reds',
      opacity,
      lighting: {
        ambient,
        diffuse,
        specular: 0.1,
        roughness: 0.9,
        fresnel: 0.2,
      },
      showscale: false,
      name: 'Sell Orders'
    };
    surfaces.push(sellSurface);
  }

  return {
    surfaces,
    layout: {
      title: '3D Auction Bidding Ribbons',
      showlegend: false,
      autosize: true,
      scene: {
        xaxis: { title: 'Round Number' },
        yaxis: { title: 'Trader Index' },
        zaxis: { title: 'Quantity (+Buy, -Sell)' },
        camera: {
          eye: { x: 1.5, y: 1.5, z: 1 },
          up: { x: 0, y: 0, z: 1 },
        },
        dragmode: 'turntable',
      },
    }
  };
}

/**
 * Get trader labels for Y-axis
 */
export function getTraderLabels(
  roundTraderElements: DeRoundTraderElement[],
  traders: DeTraderElement[]
): string[] {
  const traderMap = new Map(traders.map(t => [t.company_id, t]));
  const uniqueCompanyIds = [...new Set(roundTraderElements.map(rte => rte.cid))];

  return uniqueCompanyIds
    .map(cid => traderMap.get(cid)?.shortname || cid)
    .filter(Boolean);
}

/**
 * Validate monotonic bidding constraints
 */
export function validateMonotonicConstraints(
  roundTraderElements: DeRoundTraderElement[]
): {
  violations: Array<{
    trader: string;
    round: number;
    issue: string;
  }>;
  summary: {
    totalViolations: number;
    buyViolations: number;
    sellViolations: number;
  };
} {
  const violations: Array<{ trader: string; round: number; issue: string }> = [];

  // Group by trader
  const traderGroups = new Map<string, DeRoundTraderElement[]>();
  roundTraderElements.forEach(rte => {
    if (!traderGroups.has(rte.cid)) {
      traderGroups.set(rte.cid, []);
    }
    traderGroups.get(rte.cid)!.push(rte);
  });

  // Check each trader's progression
  traderGroups.forEach((traderData, companyId) => {
    const sortedData = traderData.sort((a, b) => a.round - b.round);

    for (let i = 1; i < sortedData.length; i++) {
      const prev = sortedData[i - 1];
      const curr = sortedData[i];

      // Only check if same order type
      if (prev.order_type === curr.order_type) {
        if (prev.order_type === OrderType.BUY && curr.quantity_int > prev.quantity_int) {
          violations.push({
            trader: companyId,
            round: curr.round,
            issue: `Buy quantity increased from ${prev.quantity_int} to ${curr.quantity_int}`
          });
        } else if (prev.order_type === OrderType.SELL && curr.quantity_int < prev.quantity_int) {
          violations.push({
            trader: companyId,
            round: curr.round,
            issue: `Sell quantity decreased from ${prev.quantity_int} to ${curr.quantity_int}`
          });
        }
      }
    }
  });

  const buyViolations = violations.filter(v => v.issue.includes('Buy')).length;
  const sellViolations = violations.filter(v => v.issue.includes('Sell')).length;

  return {
    violations,
    summary: {
      totalViolations: violations.length,
      buyViolations,
      sellViolations
    }
  };
}
