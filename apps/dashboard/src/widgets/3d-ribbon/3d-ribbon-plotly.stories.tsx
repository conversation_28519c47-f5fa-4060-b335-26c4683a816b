import type { <PERSON>a, StoryObj } from '@storybook/react';
import RibbonPlotly from './3d-ribbon-plotly';

const meta: Meta<typeof RibbonPlotly> = {
  title: 'Widgets/3D Ribbon/Plotly',
  component: RibbonPlotly,
  parameters: {
    layout: 'fullscreen',
    docs: {
      description: {
        component: `
# 3D Ribbon Chart (Plotly.js)

Interactive 3D ribbon plot component built with react-plotly.js. This component displays multiple 3D surface plots (ribbons) with comprehensive controls for customization.

## Features

- **Interactive 3D Visualization**: Multiple surface plots rendered as ribbons
- **Real-time Controls**: Adjust colorscale, opacity, and lighting parameters
- **Camera Controls**: Reset camera view and rotation limiting
- **Responsive Design**: Adapts to container size with loading states
- **Data Fetching**: Loads data from the official Plotly 3D ribbon dataset

## Controls

- **Colorscale**: Choose from various color schemes (Viridis, Cividis, Hot, Electric, Earth)
- **Opacity**: Adjust transparency of the ribbon surfaces (0.0 - 1.0)
- **Ambient Lighting**: Control ambient light intensity (0.0 - 1.0)
- **Diffuse Lighting**: Control diffuse light intensity (0.0 - 1.0)
- **Rotation Limit**: Set maximum rotation angle in degrees (0° - 360°)
- **Reset Camera**: Return to default camera position

## Technical Details

- Built with react-plotly.js for reliable 3D rendering
- Uses React hooks for state management
- Implements proper loading states and error handling
- Responsive grid layout for controls
- Dark theme styling with Tailwind CSS
        `,
      },
    },
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  name: 'Interactive 3D Ribbon Chart',
  render: () => (
    <div className="min-h-screen bg-gray-950 p-8">
      <div className="max-w-7xl mx-auto">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-white mb-2">
            3D Ribbon Chart
          </h1>
          <p className="text-gray-400 text-lg">
            Interactive 3D ribbon plot with comprehensive controls using react-plotly.js.
          </p>
        </div>
        
        <div className="bg-gray-900 rounded-lg p-6">
          <RibbonPlotly />
        </div>
        
        <div className="mt-8 bg-gray-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Usage Instructions</h2>
          <div className="text-gray-300 space-y-3">
            <p>
              <strong>Mouse Controls:</strong> Click and drag to rotate the 3D view. Use scroll wheel to zoom in/out.
            </p>
            <p>
              <strong>Parameter Controls:</strong> Use the control panel above the chart to adjust visual parameters in real-time.
            </p>
            <p>
              <strong>Camera Reset:</strong> Click "Reset Camera View" to return to the default viewing angle.
            </p>
            <p>
              <strong>Rotation Limiting:</strong> Adjust the rotation limit slider to constrain the viewing angle range.
            </p>
          </div>
        </div>
      </div>
    </div>
  ),
};

export const CompactView: Story = {
  name: 'Compact View',
  render: () => (
    <div className="bg-gray-900 p-4 rounded-lg" style={{ width: '800px', height: '600px' }}>
      <RibbonPlotly />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Compact version of the 3D ribbon chart suitable for embedding in smaller containers.',
      },
    },
  },
};
