import type { <PERSON>a, StoryObj } from '@storybook/react';
import { useState } from 'react';
import { AuctionSettingsModal } from '../src/forms/AuctionSettingsModal';
import { Button } from '../src/components/ui/button';
import { createDefaultDeSettingsValue, createEmptyDeSettingsValue } from '../src/forms/AuctionSettings.helpers';
import type { DeAuctionSaveCommand } from '../src/api-client';

const meta: Meta<typeof AuctionSettingsModal> = {
  title: 'Forms/AuctionSettingsModal',
  component: AuctionSettingsModal,
  parameters: {
    layout: 'centered',
    docs: {
      description: {
        component: 'A modal dialog for creating, viewing, and editing auction settings. Converts DeSettingsValue to DeAuctionSaveCommand for output.',
      },
    },
  },
  tags: ['autodocs'],
  argTypes: {
    mode: {
      control: 'select',
      options: ['create', 'view', 'edit'],
      description: 'Modal mode - determines available actions and initial state',
    },
    showDebugButtons: {
      control: 'boolean',
      description: 'Whether to show Clear and Default buttons for debugging',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Wrapper component to handle modal state and actions
function ModalWrapper(props: any) {
  const [isOpen, setIsOpen] = useState(false);
  const [lastCommand, setLastCommand] = useState<DeAuctionSaveCommand | null>(null);
  const [lastAction, setLastAction] = useState<string>('');

  const handleSaveAuction = (command: DeAuctionSaveCommand) => {
    setLastCommand(command);
    setLastAction('Save Auction');
    console.log('Save Auction Command:', command);
  };

  const handleSaveAsTemplate = () => {
    setLastAction('Save as Template');
    console.log('Save as Template clicked');
  };

  const handleClose = () => {
    setIsOpen(false);
    setLastAction('Close/Cancel');
  };

  return (
    <div className="p-4">
      <div className="mb-4">
        <h2 className="text-lg font-bold mb-2">Auction Settings Modal Demo</h2>
        <p className="text-sm text-gray-600 mb-4">
          Click the button below to open the modal in {props.mode} mode.
        </p>
        <Button onClick={() => setIsOpen(true)}>
          Open {props.mode} Modal
        </Button>
      </div>

      <AuctionSettingsModal
        {...props}
        open={isOpen}
        onClose={handleClose}
        onSaveAuction={handleSaveAuction}
        onSaveAsTemplate={handleSaveAsTemplate}
      />

      {/* Action Log */}
      <div className="mt-6 p-4 bg-gray-100 rounded border">
        <h3 className="font-bold mb-2">Last Action: {lastAction || 'None'}</h3>
        
        {lastCommand && (
          <div>
            <h4 className="font-semibold mb-2">DeAuctionSaveCommand:</h4>
            <pre className="text-xs bg-white p-2 rounded overflow-auto max-h-64 border">
              {JSON.stringify(lastCommand, null, 2)}
            </pre>
          </div>
        )}
      </div>
    </div>
  );
}

// Create Mode - for creating new auctions
export const CreateMode: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    mode: 'create',
    showDebugButtons: true,
    initialSettings: undefined, // Will use default settings
  },
};

// View Mode - for viewing existing auction settings (read-only initially)
export const ViewMode: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    mode: 'view',
    showDebugButtons: false,
    initialSettings: createDefaultDeSettingsValue(),
  },
};

// Edit Mode - for editing existing auction settings
export const EditMode: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    mode: 'edit',
    showDebugButtons: true,
    initialSettings: {
      ...createDefaultDeSettingsValue(),
      auction_name: "Existing Auction - Ready for Edit",
      price_label: "Price (¢/kWh)",
      quantity_label: "Power (MW)",
      round_orange_secs: 20,
      round_red_secs: 10,
    },
  },
};

// Empty Settings - testing with minimal data
export const EmptySettings: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    mode: 'create',
    showDebugButtons: true,
    initialSettings: createEmptyDeSettingsValue(),
  },
};

// Complex Settings - testing with detailed configuration
export const ComplexSettings: Story = {
  render: (args) => <ModalWrapper {...args} />,
  args: {
    mode: 'view',
    showDebugButtons: false,
    initialSettings: {
      ...createDefaultDeSettingsValue(),
      auction_name: "High-Frequency Energy Trading Auction\nMulti-line description with complex parameters",
      price_label: "Price (¢/kWh)",
      quantity_label: "Power (GW)",
      price_decimal_places: 4,
      price_change_initial: "0.0025",
      price_change_post_reversal: "0.0010",
      round_orange_secs: 5,
      round_red_secs: 2,
      starting_price_announcement_mins: 30,
      excess_level_1_label: "Low Excess (1-25 GW)",
      excess_level_2_label: "Medium Excess (26-75 GW)",
      excess_level_3_label: "High Excess (76-150 GW)",
      excess_level_4_label: "Critical Excess (>150 GW)",
      excess_level_1_quantity: "25",
      excess_level_2_quantity: "75",
      excess_level_3_quantity: "150",
      excess_level_4_quantity: "999999",
    },
  },
};
