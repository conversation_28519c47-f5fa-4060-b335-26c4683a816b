# 3D Ribbon Chart Requirements

see: https://plotly.com/javascript/ribbon-plots/

and see: https://lightningchart.com/js-charts/interactive-examples/examples/lcjs-example-0901-3dLineSeries.html


## Overview
Create a 3D ribbon chart using Plotly to visualize bidding information from the round-table data, showing how each trader's quantities change across auction rounds.

## Data Source
- **Primary Data**: `DeRoundTraderElement[]` from the round-table implementation
- **Structure**: Round × Trader × Quantity matrix with buy/sell differentiation
- **Location**: Accessed via `getDomainStore().de_auction?.blotter.round_traders`

## Visualization Requirements

### Axes Configuration
- **X-axis**: Round numbers (1, 2, 3, 4, ...)
- **Y-axis**: Trader indices or company IDs (Company A, B, C, ...)
- **Z-axis**: Quantity values
  - **Positive Z**: Buy quantities (green ribbons going "up")
  - **Negative Z**: Sell quantities (red ribbons going "down")

### Ribbon Implementation
- **Approach**: Individual trader ribbons (one Plotly `surface` trace per trader)
- **Ribbon Structure**: Each surface connects a trader's quantities across all rounds
- **Width**: Slight X-axis width (`[round, round + 0.8]`) for visual clarity
- **Continuity**: Ribbons show progression from round to round

### Color Scheme
- **Buy Orders**: Green colorscale for positive quantities
- **Sell Orders**: Red colorscale for negative quantities
- **Consistency**: Match existing round-table color conventions

### Monotonic Bidding Rules
The visualization should reflect auction monotonic bidding constraints:
- **Buy quantities**: Should generally decrease over rounds (buyers reduce bids)
- **Sell quantities**: Should generally increase over rounds (sellers raise asks)
- **Ribbon direction**: 
  - Green ribbons trend downward (decreasing buy quantities)
  - Red ribbons trend upward (increasing sell quantities, but shown as negative Z)

## Technical Implementation

### Data Structure
```typescript
interface DeRoundTraderElement {
  round: number;
  cid: string;                    // Company ID
  company_shortname: string;
  order_type: OrderType;          // 'BUY' | 'SELL'
  quantity_int: number;
  quantity_str: string;
  // ... other fields
}
```

### Plotly Surface Format
Each trader ribbon requires:
```typescript
{
  type: 'surface',
  x: [[round1, round1+0.8], [round2, round2+0.8], ...],  // 2D array
  y: [[traderIndex, traderIndex], [traderIndex, traderIndex], ...],  // 2D array
  z: [[quantity, quantity], [quantity, quantity], ...],  // 2D array (±)
  colorscale: buyerScale | sellerScale,
  showscale: false
}
```

### Data Transformation
1. **Group by trader**: Filter `round_traders` by `cid`
2. **Sort by round**: Ensure chronological order
3. **Apply sign**: Negative for SELL, positive for BUY
4. **Create surfaces**: One per trader with appropriate colorscale

## Component Structure

### File Organization
```
apps/dashboard/src/widgets/3d-ribbon/
├── RibbonChart3D.tsx           # Main component
├── RibbonChart3D.stories.tsx   # Storybook stories
├── ribbon-data-transform.ts    # Data transformation logic
├── ribbon-colors.ts            # Color schemes for buy/sell
└── index.ts                    # Exports
```

### Component Interface
```typescript
interface RibbonChart3DProps {
  width: number;
  height: number;
  // Use existing round-table data structures
  round_trader_elements: DeRoundTraderElement[];
  traders: DeTraderElement[];
}
```

## User Experience

### Interactivity
- **3D Controls**: Pan, zoom, rotate via Plotly's built-in controls
- **Hover Tooltips**: Show trader name, round, quantity, order type
- **Legend**: Optional color legend for buy/sell distinction

### Performance
- **Data Filtering**: Handle large datasets efficiently
- **Rendering**: Optimize for real-time updates from round-table

### Integration
- **Storybook**: Comprehensive examples with demo data
- **Round-table**: Potential integration as alternative view
- **Responsive**: Adapt to different screen sizes

## Visual Examples

### Expected Appearance
- **Green ribbons**: Flow from high Z (early rounds) to lower Z (later rounds) for buyers
- **Red ribbons**: Flow from low Z (early rounds) to higher Z (later rounds) for sellers
- **Separation**: Clear visual distinction between buy and sell activity
- **Trends**: Overall market direction visible through ribbon slopes

### Reference
- **Plotly Example**: https://plotly.com/javascript/ribbon-plots/
- **Data Format**: Multiple surface traces with 2D coordinate arrays
- **Inspiration**: Spectral data visualization adapted for auction bidding

## Success Criteria
1. **Accurate Data**: Correctly transforms round-table data to 3D ribbons
2. **Visual Clarity**: Clear distinction between buy/sell orders
3. **Performance**: Smooth interaction with typical auction data volumes
4. **Integration**: Works seamlessly with existing round-table infrastructure
5. **Monotonic Validation**: Visually represents bidding rule constraints
