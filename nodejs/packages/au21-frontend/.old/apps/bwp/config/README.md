March 11, 2018: moved this back again to remove the dependency between the server and the store


Sept 2017:
- deleted config.js and moved to state.store.config 
- ie: /src/model/store/config.ts
- basically --env.production sets the production switch
- the other parameters just come from the store

--------------
old:

NOTE: the file:
default.json was added 9/6/2016,
it is related to the 'config' module which the server uses!

TODO: sort out this directory

# Oct 31, 2016

a) moved files from 
- ./config to 
- ./config/_old 
- I have no idea what the files in _old are for.
  - probably and old way of configuring webpack

b) renamed `default.json` to `default.js`

c) the `default.js` file is now only being used for the server,
- webpack will trigger off 'cross-env NODE_ENV=production'

d) It's possible that I need a NODE_ENV development setting for the server to run off the /src directory
