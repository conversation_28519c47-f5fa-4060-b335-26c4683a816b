import {
  On_Activity_Row,
  On_Alert,
  On_Auction_Row,
  On_Creditor_Matrix_Cell,
  On_Creditor_Row,
  On_De_Auct_Orderbook_Row,
  On_De_Auct_Results_Row,
  On_De_Auction_Settings,
  On_De_Auction_Status,
  On_De_Auctioneer_Info,
  On_De_Blotter_Round_Cell,
  On_De_Blotter_Round_Trader_Cell,
  On_De_Blotter_Trader_Cell,
  On_De_Blotter_Update,
  On_De_Round_Trader_Edge,
  On_De_Round_Trader_Node,
  On_De_Trader_Allocation_Row,
  On_De_Trader_Clear_New_Volume,
  On_De_Trader_History_Row,
  On_De_Trader_Info,
  On_Help,
  On_Message,
  On_Mr_Auction_Settings,
  On_Mr_Auction_Status,
  On_Mr_Auctioneer_Info,
  On_Mr_Blotter_Round_Cell,
  On_Mr_Blotter_Round_Trader_Cell,
  On_Mr_Blotter_Trader_Cell,
  On_Mr_Template_Page_Detail,
  On_Mr_Template_Row,
  On_Mr_Trader_History_Row,
  On_Mr_Trader_Info,
  On_Notice,
  On_Notification,
  On_Npv_Auction_Settings,
  On_Npv_Auction_Status,
  On_Npv_Auctioneer_Bid_Row,
  On_Npv_Auctioneer_Trader_Row,
  On_Npv_Credit_Risk_Row,
  On_Npv_Trader_Bid_Row,
  On_Npv_Trader_Bid_Status,
  On_Page,
  On_Person,
  On_Pipe_Auction_Settings,
  On_Pipe_Blotter_Round_Cell,
  On_Pipe_Blotter_Round_Trader_Cell,
  On_Pipe_Blotter_Trader_Cell,
  On_Pipe_Blotter_Update,
  On_Session_Connect,
  On_Session_Ping,
  On_Session_Swept,
  On_Time,
  On_User_Session,
  On_Window,
} from './payloads'

export interface CommonRemoteState {
  on_alert: On_Alert
  on_help: On_Help
  on_notice: On_Notice
  on_page: On_Page
  on_pipe_auction_settings: On_Pipe_Auction_Settings
  on_pipe_blotter_update: On_Pipe_Blotter_Update
  on_session_connect: On_Session_Connect
  on_session_ping: On_Session_Ping
  on_session_swept: On_Session_Swept
  on_time: On_Time
  on_user_session: On_User_Session
  on_window: On_Window
  
  activity_rows: Array<On_Activity_Row>
  auction_rows: Array<On_Auction_Row>
  creditor_matrix_cells: Array<On_Creditor_Matrix_Cell>
  creditor_rows: Array<On_Creditor_Row>
  messages: Array<On_Message>
  notifications: Array<On_Notification>
  persons: Array<On_Person>
  pipe_blotter_round_cells: Array<On_Pipe_Blotter_Round_Cell>
  pipe_blotter_round_trader_cells: Array<On_Pipe_Blotter_Round_Trader_Cell>
  pipe_blotter_trader_cells: Array<On_Pipe_Blotter_Trader_Cell>
}

export interface DeRemoteState {
  on_de_auction_settings: On_De_Auction_Settings
  on_de_auction_status: On_De_Auction_Status
  on_de_auctioneer_info: On_De_Auctioneer_Info
  on_de_blotter_update: On_De_Blotter_Update
  on_de_trader_clear_new_volume: On_De_Trader_Clear_New_Volume
  on_de_trader_info: On_De_Trader_Info
  
  de_auct_orderbook_rows: Array<On_De_Auct_Orderbook_Row>
  de_auct_results_rows: Array<On_De_Auct_Results_Row>
  de_blotter_round_cells: Array<On_De_Blotter_Round_Cell>
  de_blotter_round_trader_cells: Array<On_De_Blotter_Round_Trader_Cell>
  de_blotter_trader_cells: Array<On_De_Blotter_Trader_Cell>
  de_round_trader_edges: Array<On_De_Round_Trader_Edge>
  de_round_trader_nodes: Array<On_De_Round_Trader_Node>
  de_trader_allocation_rows: Array<On_De_Trader_Allocation_Row>
  de_trader_history_rows: Array<On_De_Trader_History_Row>
}

export interface MrRemoteState {
  on_mr_auction_settings: On_Mr_Auction_Settings
  on_mr_auction_status: On_Mr_Auction_Status
  on_mr_auctioneer_info: On_Mr_Auctioneer_Info
  on_mr_template_page_detail: On_Mr_Template_Page_Detail
  on_mr_trader_info: On_Mr_Trader_Info
  
  mr_blotter_round_cells: Array<On_Mr_Blotter_Round_Cell>
  mr_blotter_round_trader_cells: Array<On_Mr_Blotter_Round_Trader_Cell>
  mr_blotter_trader_cells: Array<On_Mr_Blotter_Trader_Cell>
  mr_template_rows: Array<On_Mr_Template_Row>
  mr_trader_history_rows: Array<On_Mr_Trader_History_Row>
}

export interface NpvRemoteState {
  on_npv_auction_settings: On_Npv_Auction_Settings
  on_npv_auction_status: On_Npv_Auction_Status
  on_npv_trader_bid_status: On_Npv_Trader_Bid_Status
  
  npv_auctioneer_bid_rows: Array<On_Npv_Auctioneer_Bid_Row>
  npv_auctioneer_trader_rows: Array<On_Npv_Auctioneer_Trader_Row>
  npv_credit_risk_rows: Array<On_Npv_Credit_Risk_Row>
  npv_trader_bid_rows: Array<On_Npv_Trader_Bid_Row>
}

export interface PipeRemoteState {
  on_pipe_auction_settings: On_Pipe_Auction_Settings
  on_pipe_blotter_update: On_Pipe_Blotter_Update
  
  pipe_blotter_round_cells: Array<On_Pipe_Blotter_Round_Cell>
  pipe_blotter_round_trader_cells: Array<On_Pipe_Blotter_Round_Trader_Cell>
  pipe_blotter_trader_cells: Array<On_Pipe_Blotter_Trader_Cell>
}


export interface RemoteState {
  common: CommonRemoteState
  de: DeRemoteState
  mr: MrRemoteState
  npv: NpvRemoteState
  pipe: PipeRemoteState
}


export const remote_state: RemoteState = {
  
  common: {
    on_alert                : {
      MESSAGES : [] as any,
      TIMESTAMP: '',
    } as On_Alert,
    on_help                 : {
      AUCTION_ID: '',
      HELP_HTML : '',
    } as On_Help,
    on_notice               : {
      AUCTION_ID: '',
      NOTICE    : '',
    } as On_Notice,
    on_page                 : {
      AUCTION_DESIGN: '',
      CURRENT_PAGE  : '',
      AUCTION_ID    : '',
    } as On_Page,
    on_pipe_auction_settings: {
      AUCTION_NAME            : '',
      FIRST_ROUND_DURATION    : '',
      FOLLOWING_ROUND_DURATION: '',
      INITIAL_ELIGIBILITY     : '',
      PRICE_DECIMALS          : '',
      PRICE_DIRECTION         : '',
      PRICE_LABEL             : '',
      START_TIME              : '',
      STOP_MODE               : '',
      STOP_VOLUME             : '',
      VISIBILITY              : '',
      VOLUME_DECREMENT        : '',
      VOLUME_LABEL            : '',
      VOLUME_MINIMUM          : '',
      HIGH_CHANGE             : '',
      HIGH_LABEL              : '',
      HIGH_LIMIT              : '',
      HIGH_OPERATOR           : '',
      LOW_CHANGE              : '',
      LOW_LABEL               : '',
      MED_CHANGE              : '',
      MED_LABEL               : '',
      MED_LIMIT               : '',
      MED_OPERATOR            : '',
    } as On_Pipe_Auction_Settings,
    on_pipe_blotter_update  : {
      TIMESTAMP: '',
    } as On_Pipe_Blotter_Update,
    on_session_connect      : {
      STATUS: '',
    } as On_Session_Connect,
    on_session_ping         : {
      CLIENT_TIMESTAMP: 0,
    } as On_Session_Ping,
    on_session_swept        : {
      REASON: '',
    } as On_Session_Swept,
    on_time                 : {
      TIME: '',
    } as On_Time,
    on_user_session         : {
      SESSION_ID   : '',
      USERNAME     : '',
      USER_ID      : '',
      IS_LOGGED_IN : false,
      IS_AUCTIONEER: false,
    } as On_User_Session,
    on_window               : {
      WINDOW_NAME: '',
    } as On_Window,
    
    activity_rows: [] as { // On_On_Activity_Row
      id: string
      AUCTION_ID: string
      TIMESTAMP: string
      TIMESTAMP_FORMATTED: string
      USERNAME: string
      COMPANY: string
      ACTIVITY: string
    }[],
    
    auction_rows: [] as { // On_On_Auction_Row
      id: string
      AMPM: string
      AUCTION_ID: string
      AUCTION_NAME: string
      AUCTION_STATE: string
      AUCTION_TYPE: string
      DAY_OF_MONTH: string
      DAY_OF_WEEK: string
      HAS_DATE_TIME: string
      HOUR: string
      MINUTE: string
      MONTH: string
      TIMESTAMP: string
      IS_CLOSED: boolean
      IS_HIDDEN: boolean
      IS_SOON: boolean
    }[],
    
    creditor_matrix_cells: [] as { // On_On_Creditor_Matrix_Cell
      id: string
      SELLER_COMPANY_NAME: string
      SELLER_USER_ID: string
      SELLER_USERNAME: string
      BUYER_COMPANY_NAME: string
      BUYER_USER_ID: string
      BUYER_USERNAME: string
      CURRENT_CREDIT: string
      CURRENT_CREDIT_UNFORMATTED_MILLIONS: string
    }[],
    
    creditor_rows: [] as { // On_On_Creditor_Row
      id: string
      COUNTERPARTY_USER_ID: string
      COUNTERPARTY_USERNAME: string
      COUNTERPARTY_COMPANY_NAME: string
      CURRENT_CREDIT: string
      NEW_CREDIT: string
    }[],
    
    messages: [] as { // On_On_Message
      id: string
      FROM: string
      MESSAGE: string
      MESSAGE_TYPE: string
      AUCTION_STATE: string
      TIMESTAMP: string
      TIMESTAMP_LABEL: string
      FROM_LABEL: string
      IS_FROM_SYSTEM: boolean
      IS_FROM_AUCTIONEER: boolean
      IS_FROM_TRADER: boolean
    }[],
    
    notifications: [] as { // On_On_Notification
      id: string
      MESSAGES: Array<any>
      TIMESTAMP: string
    }[],
    
    persons: [] as { // On_On_Person
      id: string
      USER_ID: string
      COMPANY_ID: string
      IS_AUCTIONEER: boolean
      IS_ONLINE: boolean
      USERNAME: string
      PASSWORD: string
      COMPANY_NAME: string
      ROLE: string
      EMAIL: string
      PHONE: string
      CONTACT_INFO: Array<any>
    }[],
    
    pipe_blotter_round_cells: [] as { // On_On_Pipe_Blotter_Round_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      ROUND_DURATION: string
      ROUND_PRICE: string
      TOTAL_MDQ: string
      CURRENT_MAX_TERM: string
    }[],
    
    pipe_blotter_round_trader_cells: [] as { // On_On_Pipe_Blotter_Round_Trader_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      ORDER_INFO: string
      IS_BUY: boolean
      IS_SELL: boolean
      VOLUME: number
    }[],
    
    pipe_blotter_trader_cells: [] as { // On_On_Pipe_Blotter_Trader_Cell
      id: string
      AUCTION_ID: string
      IS_LOGGED_ON: boolean
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      APPROVED: boolean
      PREV_SIDE: string
      PREV_VOLUME: string
      CURR_SIDE: string
      CURR_VOLUME: string
      VALUE: string
    }[],
    
  },
  
  de: {
    on_de_auction_settings       : {
      AUCTION_ID              : '',
      AUCTION_NAME            : '',
      STARTING_TIME           : '',
      FIRST_ROUND_DURATION    : '',
      FOLLOWING_ROUND_DURATION: '',
      REPORTING_DURATION      : '',
      TRIGGER_INTERVAL        : '',
      VOLUME_LABEL            : '',
      VOLUME_MINIMUM          : '',
      VOLUME_STEP             : '',
      PRICE_LABEL             : '',
      PRICE_DECIMAL_PLACES    : '',
      STARTING_PRICE          : '',
      VALUE_MULTIPLIER        : '',
      DEFAULT_BUYER_MAX       : '',
      DEFAULT_SELLER_MAX      : '',
      _00_TO_10               : '',
      _10_TO_20               : '',
      _20_TO_30               : '',
      _30_TO_40               : '',
      _40_TO_50               : '',
      _50_TO_60               : '',
      _60_TO_70               : '',
      _70_TO_80               : '',
      _80_TO_90               : '',
      _90_PLUS                : '',
    } as On_De_Auction_Settings,
    on_de_auction_status         : {
      AUCTION_ID                : '',
      AUCTION_STATE             : '',
      AUCTION_STATE_LABEL_TOP   : '',
      AUCTION_STATE_LABEL_BOTTOM: '',
      IS_CLOSED                 : false,
      ROUND_NUMBER              : 0,
      ROUND_PRICE               : '',
      TIME_REMAINING            : '',
    } as On_De_Auction_Status,
    on_de_auctioneer_info        : {
      AUCTION_ID     : '',
      IS_AWARDABLE   : false,
      PEN_ROUND      : '',
      LAST_ROUND     : '',
      PEN_BUYERS     : '',
      LAST_BUYERS    : '',
      PEN_SELLERS    : '',
      LAST_SELLERS   : '',
      PEN_TOTAL_BUY  : '',
      LAST_TOTAL_BUY : '',
      PEN_TOTAL_SELL : '',
      LAST_TOTAL_SELL: '',
      PEN_SELL_DEC   : '',
      LAST_SELL_DEC  : '',
      PEN_MATCH      : '',
      LAST_MATCH     : '',
      PEN_EXCESS     : '',
      LAST_EXCESS    : '',
      PEN_RATIO      : '',
      LAST_RATIO     : '',
      POTENTIAL      : '',
      AWARDED_STRING : '',
    } as On_De_Auctioneer_Info,
    on_de_blotter_update         : {
      TIMESTAMP: '',
    } as On_De_Blotter_Update,
    on_de_trader_clear_new_volume: {
      NEW_VOLUME: '',
      TIMESTAMP : '',
    } as On_De_Trader_Clear_New_Volume,
    on_de_trader_info            : {
      AUCTION_ID          : '',
      ROUND_NUMBER        : '',
      ROUND_PRICE         : '',
      ORDER_STATE         : '',
      CURRENT_ORDER_STRING: '',
      NEW_VOL             : '',
      AWARDED_PRICE       : '',
      AWARDED_ROUND_NUMBER: '',
      AWARDED_VOLUME      : '',
      AWARDED_VALUE       : '',
      AWARD_LINE          : '',
      AWARD_DIRECTION     : '',
      MAX_VOL             : 0,
      MIN_VOL             : 0,
      CURRENT_VOL         : 0,
    } as On_De_Trader_Info,
    
    de_auct_orderbook_rows: [] as { // On_On_De_Auct_Orderbook_Row
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      SHOW_BUY_ORDER: boolean
      SHOW_SELL_ORDER: boolean
      BUY_TIME: string
      BUY_ORDER_ID: string
      BUY_USERNAME: string
      BUY_COMPANY_NAME: string
      BUY_VOLUME: number
      SELL_TIME: string
      SELL_ORDER_ID: string
      SELL_USERNAME: string
      SELL_COMPANY_NAME: string
      SELL_VOLUME: number
    }[],
    
    de_auct_results_rows: [] as { // On_On_De_Auct_Results_Row
      id: string
      AUCTION_ID: string
      TRADER_USERNAME: string
      TRADER_COMPANY_NAME: string
      TRADER_PENULTIMATE_ORDER: string
      TRADER_FINAL_ORDER: string
      TRADER_AWARD: string
      TRADER_AWARD_VALUE: string
    }[],
    
    de_blotter_round_cells: [] as { // On_On_De_Blotter_Round_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      ROUND_DURATION: string
      ROUND_PRICE: string
      BUYER_COUNT: string
      SELLER_COUNT: string
      BUY_VOLUME: string
      SELL_CHANGE: string
      SELL_VOLUME: string
      RAW_MATCHED: string
      MATCHED: string
      MATCH_CHANGED: string
      POTENTIAL: string
      POTENTIAL_CHANGED: string
      RATIO: string
    }[],
    
    de_blotter_round_trader_cells: [] as { // On_On_De_Blotter_Round_Trader_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      ORDER_INFO: string
      IS_BUY: boolean
      IS_SELL: boolean
      VOLUME: number
    }[],
    
    de_blotter_trader_cells: [] as { // On_On_De_Blotter_Trader_Cell
      id: string
      AUCTION_ID: string
      IS_LOGGED_ON: boolean
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      APPROVED: boolean
      PREV_SIDE: string
      PREV_VOLUME: string
      CURR_SIDE: string
      CURR_VOLUME: string
      VALUE: string
    }[],
    
    de_round_trader_edges: [] as { // On_On_De_Round_Trader_Edge
      id: string
      ROUND_NUMBER: number
      BUY_USERNAME: string
      BUY_COMPANY_NAME: string
      BUY_TIME: string
      BUY_VOLUME: number
      SELL_USERNAME: string
      SELL_COMPANY_NAME: string
      SELL_TIME: string
      SELL_VOLUME: number
      MATCH_VOLUME: number
      MATCH_POTENTIAL: number
    }[],
    
    de_round_trader_nodes: [] as { // On_On_De_Round_Trader_Node
      id: string
      ROUND_NUMBER: number
      USERNAME: string
      COMPANY_NAME: string
      SELL_POTENTIAL: number
      SELL_VOLUME: number
      SELL_MATCH: number
      BUY_POTENTIAL: number
      BUY_VOLUME: number
      BUY_MATCH: number
      FULLY_OPPOSED_MATCH: number
    }[],
    
    de_trader_allocation_rows: [] as { // On_On_De_Trader_Allocation_Row
      id: string
      AUCTION_ID: string
      COUNTER_PARTY_NAME: string
      MATCH_VOLUME: string
    }[],
    
    de_trader_history_rows: [] as { // On_On_De_Trader_History_Row
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: string
      ROUND_PRICE: string
      PRICE_SUFFIX: string
      ORDER_SIDE: string
      VOLUME: string
      VALUE: string
      SELLER_RANGE: string
      MATCH_RANGE: string
    }[],
    
  },
  
  mr: {
    on_mr_auction_settings    : {
      AUCTION_NAME            : '',
      FIRST_ROUND_DURATION    : '',
      FOLLOWING_ROUND_DURATION: '',
      INITIAL_ELIGIBILITY     : '',
      PRICE_DECIMALS          : '',
      PRICE_DIRECTION         : '',
      PRICE_LABEL             : '',
      START_TIME              : '',
      STOP_MODE               : '',
      STOP_VOLUME             : '',
      VISIBILITY              : '',
      VOLUME_DECREMENT        : '',
      VOLUME_LABEL            : '',
      VOLUME_MINIMUM          : '',
      HIGH_CHANGE             : '',
      HIGH_LABEL              : '',
      HIGH_LIMIT              : '',
      HIGH_OPERATOR           : '',
      LOW_CHANGE              : '',
      LOW_LABEL               : '',
      MED_CHANGE              : '',
      MED_LABEL               : '',
      MED_LIMIT               : '',
      MED_OPERATOR            : '',
    } as On_Mr_Auction_Settings,
    on_mr_auction_status      : {
      AUCTION_ID         : '',
      AUCTION_STATE      : '',
      AUCTION_STATE_LABEL: '',
      IS_CLOSED          : false,
      ROUND_NUMBER       : 0,
      ROUND_PRICE        : '',
      TIME_REMAINING     : '',
    } as On_Mr_Auction_Status,
    on_mr_auctioneer_info     : {
      AUCTION_ID       : '',
      IS_AWARDABLE     : false,
      PEN_ROUND        : '',
      LAST_ROUND       : '',
      PEN_TRADER_COUNT : '',
      LAST_TRADER_COUNT: '',
      PEN_TOTAL        : '',
      LAST_TOTAL       : '',
      PEN_DEC          : '',
      LAST_DEC         : '',
      PEN_EXCESS       : '',
      LAST_EXCESS      : '',
      PEN_RATIO        : '',
      LAST_RATIO       : '',
      POTENTIAL        : '',
      AWARDED_STRING   : '',
    } as On_Mr_Auctioneer_Info,
    on_mr_template_page_detail: {
      TEMPLATE_ID             : '',
      FIRST_ROUND_DURATION    : '',
      FOLLOWING_ROUND_DURATION: '',
      INITIAL_ELIGIBILITY     : '',
      PRICE_DECIMALS          : '',
      PRICE_DIRECTION         : '',
      PRICE_LABEL             : '',
      START_TIME              : '',
      STOP_MODE               : '',
      STOP_VOLUME             : '',
      VISIBILITY              : '',
      VOLUME_DECREMENT        : '',
      VOLUME_LABEL            : '',
      VOLUME_MINIMUM          : '',
      HIGH_CHANGE             : '',
      HIGH_LABEL              : '',
      HIGH_LIMIT              : '',
      HIGH_OPERATOR           : '',
      LOW_CHANGE              : '',
      LOW_LABEL               : '',
      MED_CHANGE              : '',
      MED_LABEL               : '',
      MED_LIMIT               : '',
      MED_OPERATOR            : '',
      PRICE_CHANGE_GT_1X      : '',
      PRICE_CHANGE_GT_2X      : '',
      PRICE_CHANGE_GT_3X      : '',
      PRICE_CHANGE_GT_4X      : '',
    } as On_Mr_Template_Page_Detail,
    on_mr_trader_info         : {
      AUCTION_ID         : '',
      AWARD_VOL          : '',
      AWARD_PRICE        : '',
      AWARD_ROUND_NUMBER : '',
      AWARD_VALUE        : '',
      INITIAL_ELIGIBILITY: '',
      CURRENT_ELIGIBILITY: '',
      CURRENT_VOLUME     : '',
      IS_BLINDED         : false,
      SHOW_AWARD_PANEL   : false,
    } as On_Mr_Trader_Info,
    
    mr_blotter_round_cells: [] as { // On_On_Mr_Blotter_Round_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      PRICE: string
      TOTAL_VOLUME: string
      ACTIVITY: string
      IS_AWARD: boolean
    }[],
    
    mr_blotter_round_trader_cells: [] as { // On_On_Mr_Blotter_Round_Trader_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      VOLUME: string
    }[],
    
    mr_blotter_trader_cells: [] as { // On_On_Mr_Blotter_Trader_Cell
      id: string
      AUCTION_ID: string
      IS_LOGGED_ON: boolean
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      ELIGIBILITY: string
    }[],
    
    mr_template_rows: [] as { // On_On_Mr_Template_Row
      id: string
      TEMPLATE_ID: string
      TEMPLATE_NAME: string
    }[],
    
    mr_trader_history_rows: [] as { // On_On_Mr_Trader_History_Row
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: string
      ROUND_PRICE: string
      VOLUME: string
      VALUE: string
      ACTIVITY: string
    }[],
    
  },
  
  npv: {
    on_npv_auction_settings : {
      ALLOW_PRORATION         : false,
      ALLOW_TIME_EXTENSION    : false,
      ALLOW_WITHDRAWAL        : false,
      BID_MUST_IMPROVE_HIGHEST: false,
      BID_MUST_IMPROVE_OWN    : false,
      AUCTION_DESIGN          : '',
      AUCTION_ID              : '',
      AUCTION_NAME            : '',
      BID_CONSTRAINTS_HTML    : '',
      BID_RANKING_RULE        : '',
      CONTRACT_START_MONTH    : '',
      CONTRACT_START_YEAR     : '',
      DISCOUNT_RATE           : '',
      NPV_DAYS_PER_MONTH      : '',
      RATE_MAX                : '',
      RATE_MIN                : '',
      RATE_THRESHOLD_1        : '',
      RATE_THRESHOLD_2        : '',
      RATE_THRESHOLD_3        : '',
      RATE_THRESHOLD_4        : '',
      TERM_MAX_AT_MAX_RATE    : '',
      TERM_MIN                : '',
      TERM_THRESHOLD_1        : '',
      TERM_THRESHOLD_2        : '',
      TERM_THRESHOLD_3        : '',
      TERM_THRESHOLD_4        : '',
      THRESHOLD_COUNT         : '',
      TIME_EXTENSION          : '',
      USE_ACTUAL_DAYS         : '',
      VOLUME_MAX              : '',
      VOLUME_MIN              : '',
      WINDOW_COUNT            : '',
      WINDOW_ONE_CLOSING_TIME : '',
      WINDOW_ONE_OPENING_TIME : '',
      WINDOW_TWO_CLOSING_TIME : '',
      WINDOW_TWO_OPENING_TIME : '',
      WITHDRAWAL_LIMIT        : '',
    } as On_Npv_Auction_Settings,
    on_npv_auction_status   : {
      AUCTION_ID                : '',
      AUCTION_NAME              : '',
      IS_CLOSED                 : false,
      AUCTION_STATE             : '',
      AUCTION_STATE_LABEL_TOP   : '',
      AUCTION_STATE_LABEL_BOTTOM: '',
      ALLOCATION                : '',
    } as On_Npv_Auction_Status,
    on_npv_trader_bid_status: {
      ALLOW_PRORATION      : false,
      ANNUAL_RATE          : '',
      BID_ALLOCATION       : '',
      BID_NPV              : '',
      BID_PRICE            : '',
      BID_RANK             : '',
      BID_RISK_ADJUSTED_NPV: '',
      BID_TERM             : '',
      BID_VOLUME_MAX       : '',
      BID_VOLUME_MIN       : '',
      CREDIT_LIMIT         : '',
      DEFAULT_RISK_FACTOR  : '',
      REVISED              : '',
      TIMESTAMP            : '',
      TIMESTAMP_FORMATTED  : '',
      TOTAL_EXPOSURE       : '',
      USERNAME             : '',
      WITHDRAWN            : '',
    } as On_Npv_Trader_Bid_Status,
    
    npv_auctioneer_bid_rows: [] as { // On_On_Npv_Auctioneer_Bid_Row
      id: string
      ALLOCATION: string
      BID_NPV: string
      BID_PRICE: string
      BID_TERM: string
      BID_VOLUME_MIN: string
      BID_VOLUME_MAX: string
      COMPANY: string
      RANK: number
      TIMESTAMP: string
      TIMESTAMP_FORMATTED: string
      USERNAME: string
      USER_ID: string
      WITHDRAWN: boolean
    }[],
    
    npv_auctioneer_trader_rows: [] as { // On_On_Npv_Auctioneer_Trader_Row
      id: string
      COMPANY_NAME: string
      USER_ID: string
      USERNAME: string
      IS_ONLINE: boolean
    }[],
    
    npv_credit_risk_rows: [] as { // On_On_Npv_Credit_Risk_Row
      id: string
      USER_ID: string
      USERNAME: string
      CREDIT_LIMIT: string
      DEFAULT_RISK_FACTOR: string
    }[],
    
    npv_trader_bid_rows: [] as { // On_On_Npv_Trader_Bid_Row
      id: string
      AUCTION_ID: string
      TIMESTAMP: string
      TIMESTAMP_FORMATTED: string
      USERNAME: string
      BID_ALLOCATION: string
      BID_NPV: string
      BID_PRICE: string
      BID_RANK: number
      BID_TERM: string
      BID_VOLUME_MAX: string
      BID_VOLUME_MIN: string
      REVISED: boolean
      WITHDRAWN: boolean
    }[],
    
  },
  
  pipe: {
    on_pipe_auction_settings: {
      AUCTION_NAME            : '',
      FIRST_ROUND_DURATION    : '',
      FOLLOWING_ROUND_DURATION: '',
      INITIAL_ELIGIBILITY     : '',
      PRICE_DECIMALS          : '',
      PRICE_DIRECTION         : '',
      PRICE_LABEL             : '',
      START_TIME              : '',
      STOP_MODE               : '',
      STOP_VOLUME             : '',
      VISIBILITY              : '',
      VOLUME_DECREMENT        : '',
      VOLUME_LABEL            : '',
      VOLUME_MINIMUM          : '',
      HIGH_CHANGE             : '',
      HIGH_LABEL              : '',
      HIGH_LIMIT              : '',
      HIGH_OPERATOR           : '',
      LOW_CHANGE              : '',
      LOW_LABEL               : '',
      MED_CHANGE              : '',
      MED_LABEL               : '',
      MED_LIMIT               : '',
      MED_OPERATOR            : '',
    } as On_Pipe_Auction_Settings,
    on_pipe_blotter_update  : {
      TIMESTAMP: '',
    } as On_Pipe_Blotter_Update,
    
    pipe_blotter_round_cells: [] as { // On_On_Pipe_Blotter_Round_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      ROUND_DURATION: string
      ROUND_PRICE: string
      TOTAL_MDQ: string
      CURRENT_MAX_TERM: string
    }[],
    
    pipe_blotter_round_trader_cells: [] as { // On_On_Pipe_Blotter_Round_Trader_Cell
      id: string
      AUCTION_ID: string
      ROUND_NUMBER: number
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      ORDER_INFO: string
      IS_BUY: boolean
      IS_SELL: boolean
      VOLUME: number
    }[],
    
    pipe_blotter_trader_cells: [] as { // On_On_Pipe_Blotter_Trader_Cell
      id: string
      AUCTION_ID: string
      IS_LOGGED_ON: boolean
      USER_ID: string
      USERNAME: string
      COMPANY_NAME: string
      APPROVED: boolean
      PREV_SIDE: string
      PREV_VOLUME: string
      CURR_SIDE: string
      CURR_VOLUME: string
      VALUE: string
    }[],
    
  },
}

