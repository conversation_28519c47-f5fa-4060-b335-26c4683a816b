export interface MrCreateAuction {
    AUCTION_NAME: string
    AUCTIONID: number
    DIRECTION: string
    FOLLOWING_ROUND_DURATION: string
    HIGH_CHANGE: string
    HIGH_LABEL: string
    HIGH_LIMIT: string
    HIGH_OPERATOR: string
    INITIAL_ELIGIBILITY: string
    INITIAL_ROUND_DURATION: string
    LOW_CHANGE: string
    LOW_LABEL: string
    MED_CHANGE: string
    MED_LABEL: string
    MED_LIMIT: string
    MED_OPERATOR: string
    MIN_VOL: string
    PRICE_DECIMALS: string
    PRICE_LABEL: string
    START_TIME: string
    STOP_MODE: string
    STOP_VOLUME: string
    VISIBILITY: string
    VOLUME_DECREMENT: string
    VOLUME_LABEL: string
}

export function create_sample_auction(auction_name: string) {
    return {
        AUCTION_NAME: auction_name,
        AUCTIONID: 0,
        DIRECTION: 'UP',
        FOLLOWING_ROUND_DURATION: '60',
        HIGH_CHANGE: '0.0030',
        HIGH_LABEL: 'H',
        HIGH_LIMIT: '200,000',
        HIGH_OPERATOR: 'GT',
        INITIAL_ELIGIBILITY: '100,000',
        INITIAL_ROUND_DURATION: '60',
        LOW_CHANGE: '0.0010',
        LOW_LABEL: 'L',
        MED_CHANGE: '0.0020',
        MED_LABEL: 'M',
        MED_LIMIT: '150,000',
        MED_OPERATOR: 'GT',
        MIN_VOL: '1',
        PRICE_DECIMALS: '4',
        PRICE_LABEL: '$/Dth',
        START_TIME: '02/28/2019 - 23:38:00',
        STOP_MODE: 'ZERO',
        STOP_VOLUME: '0',
        VISIBILITY: 'ALL',
        VOLUME_DECREMENT: '1',
        VOLUME_LABEL: 'Dth/day'
    }
}
