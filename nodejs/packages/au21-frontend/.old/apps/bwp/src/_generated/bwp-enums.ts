export type Activity =
    | 'HIGH'
    | 'MED'
    | 'LOW'

export type AuctionDesign =
    | 'MULTI_ROUND'
    | 'ROFR'

export type MessageType =
    | 'AUCTIONEER_TO_TRADER'
    | 'TRADER_TO_AUCTIONEER'
    | 'AUCTIONEER_TO_ALL'

export type Operator =
    | 'GT'
    | 'GE'

export type PriceDirection =
    | 'UP'
    | 'DOWN'

export enum Role {
    ADMIN = 'ADMIN',
    AUCTIONEER = 'AUCTIONEER',
    TRADER = 'TRADER',
    INTERNAL_OBSERVER = 'INTERNAL_OBSERVER',
    EXTERNAL_OBSERVER = 'EXTERNAL_OBSERVER',
}

export type SessionOfflineReason =
    | 'LOGGED_OFF'
    | 'PAGE_UNLOADED'
    | 'HIGH_LATENCY' // ie: client triggered due to not receiving server message
    | 'SESSION_STALE'  // ie: a request with a non-active session id
    | 'FORCED_OFF'  // ie: manually booted off
    | 'SWEPT'  // ie: server triggered due to no ping

export type TradeType =
    | 'BUY'
    | 'SELL'


// MR types

export type  MRAuctioneerPageFixedBlotter =
    | 'ROUNDPRICE'
    | 'TOTALVOL'
    | 'ACTIVITY'
    | 'BIDDERS'

export type MRAwardPageFixedRow =
    | 'ROUND_PRICE'
    | 'TOTALVOL'
    | 'BIDDERS'

// bwp_command(a.connector, "GO")

export enum MRCommand {
    GO = 'GO',
    PAUSE = 'PAUSE',
    RESTART_ROUND = 'RESTART_ROUND',
    END_ROUND = 'END_ROUND',
    NEXT_ROUND = 'NEXT_ROUND',
}

// for first round, Price shown -> bidders when it is set.
// for following rounds, price shown -> bidders when clock is running.
export enum MRState {
    ROUND_INIT = 'ROUND_INIT',
    ROUND_PRICE_SET = 'ROUND_PRICE_SET',
    ROUND_CLOCK_RUNNING = 'ROUND_CLOCK_RUNNING',
    ROUND_CLOCK_PAUSED = 'ROUND_CLOCK_PAUSED',
    ROUND_CLOSED = 'ROUND_CLOSED',
    AWARDING_AUCTION = 'AWARDING_AUCTION',
    AUCTION_CLOSED = 'AUCTION_CLOSED',
}

export enum MRStopMode {
    LT = 'LT',
    LE = 'LE',
    ZERO = 'ZERO',
}

export type  MRVisibility =
    | 'ALL'
    | 'FIRST_ROUND'
    | 'ELIGIBILITY'

export enum MRClockLabel {
    PRICE = 'PRICE',
    TERM = 'TERM'
}

