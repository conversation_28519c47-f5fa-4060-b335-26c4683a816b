import {
    MRStopMode,
    MRVisibility,
    Operator,
    PriceDirection,
    Role,
    MRState, MR<PERSON><PERSON><PERSON>abel,
} from './bwp-enums'
import {MrPage} from '../components/Auction/__demo-helpers/MrPage'

export class AuPayload {
    EVENT = ''
    _COUNT = 0
}

export class OnAlert extends AuPayload {
    BODY = ''
    TITLE = ''
}

// export class OnAuctionClosed extends AuPayload {
// }

export class OnAuctionRow extends AuPayload {
    AMPM = ''
    AUCTION_NAME = ''
    AUCTION_ROW_ID = 0
    AUCTION_TYPE = ''
    DAY_OF_MONTH = 0
    DAY_OF_WEEK = 0
    HAS_DATE_TIME = false
    HOUR = ''
    IS_CLOSED = false
    IS_DELETED = false
    IS_HIDDEN = false
    IS_SOON = false
    MINUTE = 0
    MONTH = 0
    TIMESTAMP = 0 // used for sorting

    /*
        public function OnAuctionRow (e :AtClient) {

              AUCTIONID = e.AUCTION_ROW_ID
              AUCTION_NAME = e.AUCTION_NAME
              AUCTION_STATUS = e.AUCTION_STATUS
              AUCTION_TYPE = e.AUCTION_TYPE

              HAS_DATE_TIME = Validator.isTrue (e.HAS_DATE_TIME)

              TIMESTAMP = Number (e.TIMESTAMP)

              switch (Number (e.MONTH)) {
                  case 1: 	MONTH = 'Jan'; break
                  case 2: 	MONTH = 'Feb'; break
                  case 3: 	MONTH = 'Mar'; break
                  case 4: 	MONTH = 'Apr'; break
                  case 5: 	MONTH = 'May'; break
                  case 6: 	MONTH = 'Jun'; break
                  case 7: 	MONTH = 'Jul'; break
                  case 8: 	MONTH = 'Aug'; break
                  case 9: 	MONTH = 'Sep'; break
                  case 10: 	MONTH = 'Oct'; break
                  case 11: 	MONTH = 'Nov'; break
                  case 12: 	MONTH = 'Dec'; break
              }

              DAY_OF_MONTH = e.DAY_OF_MONTH

              switch (Number (e.DAY_OF_WEEK)) {
                  case 1: DAY_OF_WEEK = 'Monday'; break
                  case 2: DAY_OF_WEEK = 'Tuesday'; break
                  case 3: DAY_OF_WEEK = 'Wednesday'; break
                  case 4: DAY_OF_WEEK = 'Thursday'; break
                  case 5: DAY_OF_WEEK = 'Friday'; break
                  case 6: DAY_OF_WEEK = 'Saturday'; break
                  case 7: DAY_OF_WEEK = 'Sunday'; break
              }

              if (e.HOUR == '') {
                  TIME = ''
              }
              else {
                  TIME = e.HOUR + ":" + (Number (e.MINUTE) < 10 ? "0" : "") + e.MINUTE + ' ' + e.AMPM
              }

              IS_SOON		= e.IS_SOON

              IS_CLOSED  = String (e.IS_CLOSED) == 'true'
              IS_DELETED = String (e.IS_DELETED) == 'true'
              IS_HIDDEN  = String (e.IS_HIDDEN) ==  'true'
              IS_NOT_HIDDEN = ! IS_HIDDEN

              if (MONTH == null) MONTH = ''
              if (DAY_OF_MONTH == null) DAY_OF_MONTH = ''

          }

     */

}

export class OnAuctionUserRow extends AuPayload {
    AUCTIONID = 0
    COMPANY = ''
    FIRSTNAME = ''
    LASTNAME = ''
    ROLE: Role = null
    USERID = 0
    USERNAME = ''
}


export class OnHumanizedAlert extends AuPayload {
    msg = ''
}

export class OnLogin extends AuPayload {
    ROLE: Role = null
    USERNAME = ''
}

export class OnMessage extends AuPayload {
    AUCTIONID = 0
    DATE = ''
    SENDER = ''
    TEXT = ''
    TIME = ''
    TIMESTAMP = ''
}

export class OnMrAuctioneerSummary extends AuPayload {
    PAGE = ''
    AUCTIONID = 0

    // bids:
    BIDS_CURRENT_ROUND = ''
    BIDS_FIRST_ROUND = ''
    BIDS_PREV_ROUND = ''

    // volume:
    VOLUME_CURRENT_ROUND = ''
    VOLUME_POTENTIAL = ''
    VOLUME_PREVIOUS_ROUND = ''
    VOLUME_REDUCED = ''

    STOP_VOLUME_LABEL = ''
}

export class OnMrAuctionName extends AuPayload {
    AUCTIONID = 0
    AUCTION_NAME = ''
}

export class OnMrAuctionSettings extends AuPayload {
    AUCTION_NAME = ''
    AUCTIONID = 0
    CLOCK_LABEL: MRClockLabel = null
    DIRECTION: PriceDirection = null
    FOLLOWING_ROUND_DURATION = '0'
    HIGH_CHANGE = ''
    HIGH_LABEL = ''
    HIGH_LIMIT = ''
    HIGH_OPERATOR: Operator = null
    INITIAL_ELIGIBILITY = ''
    INITIAL_ROUND_DURATION = '0'
    LOW_CHANGE = ''
    LOW_LABEL = ''
    MED_CHANGE = ''
    MED_LABEL = ''
    MED_LIMIT = ''
    MED_OPERATOR: Operator = null
    MIN_VOL = ''
    PRICE_DECIMALS = ''
    PRICE_LABEL = ''
    START_TIME = ''
    STOP_MODE: MRStopMode = null
    STOP_VOLUME = ''
    VISIBILITY: MRVisibility = null
    VOLUME_DECREMENT = ''
    VOLUME_LABEL = ''
}

export class OnMrAuctionStatus extends AuPayload {
    AUCTIONID = 0
    AUCTION_NAME = ''
    AUCTION_STATUS = ''
    PRICE_LABEL = ''
    ROUND_CONTROLLER_STATUS = null as MRState
    ROUND_NUMBER = 0
    ROUND_PRICE = ''
    VOLUME_LABEL = ''
}

export class OnMrAward extends AuPayload {
    AUCTIONID = 0
    TEXT = ''
}

export class OnMrAwardRow extends AuPayload {
    AUCTIONID = 0
    AWARD = ''
    Col1 = ''
    COMPANY = ''
    FINAL = ''
    OID = 0
    PREVIOUS = ''
    SORT_INDEX = 0
    USERNAME = ''
}

export class OnMrBidderBlotterRow extends AuPayload {
    AUCTIONID = 0
    COMPANY = ''
    Col1 = ''
    ELIGIBILITY = ''
    ELIGIBILITY_NUM = 0
    IS_ONLINE = false
    HAS_SEEN_AUCTION = false
    OID = 0
    SORT_INDEX = 0
    USERNAME = ''

    Round_1 = ''
    Round_2 = ''
    Round_3 = ''
    Round_4 = ''
    Round_5 = ''
    Round_6 = ''
    Round_7 = ''
    Round_8 = ''
    Round_9 = ''
    Round_10 = ''
    Round_11 = ''
    Round_12 = ''
    Round_13 = ''
    Round_14 = ''
    Round_15 = ''
    Round_16 = ''
    Round_17 = ''
    Round_18 = ''
    Round_19 = ''
    Round_20 = ''
    Round_21 = ''
    Round_22 = ''
    Round_23 = ''
    Round_24 = ''
    Round_25 = ''
    Round_26 = ''
    Round_27 = ''
    Round_28 = ''
    Round_29 = ''
    Round_30 = ''
    Round_31 = ''
    Round_32 = ''
    Round_33 = ''
    Round_34 = ''
    Round_35 = ''
    Round_36 = ''
    Round_37 = ''
    Round_38 = ''
    Round_39 = ''
    Round_40 = ''
    Round_41 = ''
    Round_42 = ''
    Round_43 = ''
    Round_44 = ''
    Round_45 = ''
    Round_46 = ''
    Round_47 = ''
    Round_48 = ''
    Round_49 = ''
    Round_50 = ''
}

export class OnMrBidRow extends AuPayload {
    AUCTIONID = 0
    BID_VOLUME = ''
    ROUND_ACTIVITY = ''
    ROUND_NUMBER = 0
    ROUND_PRICE = ''
    // TIMESTAMP = ''
    // USERNAME = ''
}

export class OnMrBidderStatus extends AuPayload {
    // TODO: should have IS_BLINDED property
    AUCTIONID = 0
    AUCTION_NAME = ''
    AUCTION_STATUS = ''
    CURRENT_BID = ''
    CURRENT_ELIGIBILITY = ''
    PRICE_LABEL = ''
    ROUND_NUMBER = ''
    ROUND_PRICE = ''
    VOLUME_LABEL = ''
}

export class OnMrBlinding extends AuPayload {
    AUCTIONID = 0
    IS_BLINDED = false
}

export class OnMrClearAuctioneerPageBidTable extends AuPayload {
    AUCTIONID = 0
}

export class OnMrClearAwardPage extends AuPayload {
    AUCTIONID = 0
    AUCTION_NAME = ''
}

export class OnMrFixedBlotterRow extends AuPayload {
    AUCTIONID = 0
    COMPANY = ''
    Col1 = ''
    ELIGIBILITY = ''
    ELIGIBILITY_NUM = 0
    OID = ''
    SORT_INDEX = 0
    USERNAME = ''

    Round_1 = ''
    Round_2 = ''
    Round_3 = ''
    Round_4 = ''
    Round_5 = ''
    Round_6 = ''
    Round_7 = ''
    Round_8 = ''
    Round_9 = ''
    Round_10 = ''
    Round_11 = ''
    Round_12 = ''
    Round_13 = ''
    Round_14 = ''
    Round_15 = ''
    Round_16 = ''
    Round_17 = ''
    Round_18 = ''
    Round_19 = ''
    Round_20 = ''
    Round_21 = ''
    Round_22 = ''
    Round_23 = ''
    Round_24 = ''
    Round_25 = ''
    Round_26 = ''
    Round_27 = ''
    Round_28 = ''
    Round_29 = ''
    Round_30 = ''
    Round_31 = ''
    Round_32 = ''
    Round_33 = ''
    Round_34 = ''
    Round_35 = ''
    Round_36 = ''
    Round_37 = ''
    Round_38 = ''
    Round_39 = ''
    Round_40 = ''
    Round_41 = ''
    Round_42 = ''
    Round_43 = ''
    Round_44 = ''
    Round_45 = ''
    Round_46 = ''
    Round_47 = ''
    Round_48 = ''
    Round_49 = ''
    Round_50 = ''
}

export class OnMrRemoveUserRow extends AuPayload {
    AUCTIONID = 0
    USERID = 0
}

export class OnForceHome extends AuPayload {

}

export class OnMrRoundNumber extends AuPayload {
    AUCTIONID = 0
    ROUND_NUMBER = 0
}

export class OnMrSubmittedBid extends AuPayload {
    AUCTIONID = 0
    BID_VOLUME = ''
    PAGE = ''
}

export class OnMrTarget extends AuPayload {
    AUCTIONID = 0
    TARGET = ''
}

export class OnMrTemplate extends AuPayload {
    DELETED = false
    DESCRIPTION = ''
    DIRECTION: PriceDirection = null
    FOLLOWING_ROUND_DURATION = ''
    HIGH_CHANGE = ''
    HIGH_LABEL = ''
    HIGH_LIMIT = ''
    HIGH_OPERATOR: Operator = null
    INITIAL_ELIGIBILITY = ''
    INITIAL_ROUND_DURATION = 0
    LOW_CHANGE = ''
    LOW_LABEL = ''
    MED_CHANGE = ''
    MED_LABEL = ''
    MED_LIMIT = ''
    MED_OPERATOR: Operator = null
    MIN_VOL = ''
    PRICE_DECIMALS = ''
    PRICE_LABEL = ''
    STOP_MODE: MRStopMode = null
    STOP_VOLUME = ''
    TEMPLATEID = 0
    VISIBILITY: MRVisibility = null
    VOLUME_DECREMENT = ''
    VOLUME_LABEL = ''
}

export class OnMrTraderSettings extends AuPayload {
    AUCTIONID: number
    CLOCK_LABEL: MRClockLabel = null
    DESCRIPTION = ''
    ELIGIBILITY = ''
    HIGH_CHANGE = ''
    HIGH_LABEL = ''
    HIGH_LIMIT = ''
    LOW_CHANGE = ''
    LOW_LABEL = ''
    LOW_LIMIT = ''
    MED_CHANGE = ''
    MED_LABEL = ''
    MED_LIMIT = ''
    PRICE_DIRECTION = ''
    ROUND_DURATION = ''
    VISIBILITY = ''
}


export class OnMrUpdateBid extends AuPayload {
    ACTIVITY = ''
    AUCTIONID = 0
    BID_VOLUME = ''
    ROUND_NUMBER = ''
    TOTALVOL = ''
    USERID = 0
}

export class OnNextPage extends AuPayload {
    NEXT_PAGE: MrPage = null
    NEXT_AUCTION_ID = 0
    HAS_INITED = false
}


export class OnNotice extends AuPayload {
    AUCTIONID = 0
    TEXT = ''
}


export class OnReload extends AuPayload {
    REASON = ''
}

export class OnRemoveAuctionRow extends AuPayload {
    AUCTIONID = 0
}


export class OnSessionRow extends AuPayload {
    AUCTION_NAME = ''
    OFFLINE_REASON = ''
    PAGE = ''
    REMOTE_ADDRESS = ''
    SESSION_AUCTIONID = 0
    SESSIONID = ''
    USER_AGENT = ''
    USERNAME = ''
}

export class OnShowProgress extends AuPayload {
}


export class OnSystemMessage extends AuPayload {
    TIMESTAMP = ''
    TEXT = ''
    MESSAGEID = 0
    HUMANIZE = false
}

export class OnTime extends AuPayload {
    YEAR = 0
    MONTH = 0
    DATE = 0
    DAY = 0
    HOUR = 0
    MINS = 0
    SECS = 0
}

export class OnUserOnline extends AuPayload {
    USERID = 0
    USERNAME = ''
    IS_ONLINE = false
}


export class OnUserRow extends AuPayload {
    COMPANY = ''
    DELETED = false // removes from the userlist
    EMAIL = ''
    FIRSTNAME = ''
    // IS_ONLINE = false // TODO: this will be in next version
    LASTNAME = ''
    MOBILE = ''
    PASSWORD = ''
    ROLE: Role = null
    USERID = 0
    USERNAME = ''
    WORK = ''
    // roleLabel = ''
}


