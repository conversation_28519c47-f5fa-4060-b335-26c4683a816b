import '../../polyfills/polyfills'
import Vue from 'vue'
import MainApp from './MainApp.vue'

import '../../assets/style.less'
import '../../plugins/ant-design-vue-plugin/ant-design-vue-plugin'
import '../../plugins/vue-quill-editor-plugin/quill-editor-plugin'
import { StorePlugin } from '../../plugins/store-plugin/store-plugin'
import { ConnectorPlugin } from '../../plugins/connector-plugin/connector-plugin'
import { LocalStorePlugin } from '../../plugins/local-store-plugin/local-store-plugin'
import { ColorThemePlugin } from '../../plugins/color-plugin/color-plugin'

Vue.config.productionTip = false
Vue.config.devtools = true
Vue.config.performance = true

Vue.use(StorePlugin)
Vue.use(ConnectorPlugin)
Vue.use(LocalStorePlugin)
Vue.use(ColorThemePlugin)


new Vue({
  render: h => h(MainApp),
}).$mount('#app')
