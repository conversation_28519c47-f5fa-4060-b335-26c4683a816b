<template>
    <div class="MainApp flex-center">
        <div class="MainApp__container">
            <PageLayout style="width: 100%; height: 100%">
                <template v-if="has_inited">
                     <LoginPage v-if="next_page === MrPage.LoginPage"/>
                     <HomePage v-if="next_page === MrPage.HomePage"/>
                     <SessionsPage v-if="next_page === MrPage.SessionPage"/>
                     <UserPage v-if="next_page === MrPage.UserPage"/>
                     <AuctionPage v-if="next_page === MrPage.MrAuctioneerPage"/>
                     <BidderPage v-if="next_page === MrPage.MrTraderPage" style="width: 100%; height: 100%; display: flex; flex-direction: column; justify-content: stretch;"/>
                     <AuctionTemplatePage v-if="next_page === MrPage.MrTemplatePage"/>
                     <AwardPage v-if="next_page === MrPage.MrAwardPage"/>
                </template>
            </PageLayout>
        </div>

        <a-modal
          v-if="longError"
          :visible="true"
          title="Error"
          @cancel="longError = null"
          closable
          centered
          width="450px"
        >
            <div style="padding: 16px">
                <div style="white-space: pre-wrap">{{longError.title}}</div>
                <div style="white-space: pre-wrap">{{longError.body}}</div>
            </div>

            <a-button
              slot="footer"
              type="dashed"
              @click="longError = null"
            >
                Ok
            </a-button>
        </a-modal>
    </div>
</template>

<script lang="ts">
    import {Component, Vue, Watch} from 'vue-property-decorator'
    import LoginPage from '../../components/Login/LoginPage.vue'
    import {BwpConnector} from '../../services/bwp-connector/connector'
    import {auStoreInjectKey} from '../../plugins/store-plugin/store-plugin'
    import {auConnectorInjectKey} from '../../plugins/connector-plugin/connector-plugin'
    import {BwpStore} from '../../store/store'
    import {BwpModel} from '../../store/model'
    import {OnAlert} from '../../_generated/server_outputs'
    import {MrPage} from '../../components/Auction/__demo-helpers/MrPage'
    import AuctionPage from '../../components/Auction/AuctionPage/AuctionPage.vue'
    import HomePage from '../../components/Pages/HomePage/HomePage.vue'
    import SessionsPage from '../../components/Auction/SessionsPage/SessionPage.vue'
    import UserPage from '../../components/User/UserPage/UserPage.vue'
    import {auLocalStoreInjectKey} from '../../plugins/local-store-plugin/local-store-plugin'
    import {LocalStore} from '../../plugins/local-store-plugin/LocalStore'
    import BidderPage from '../../components/Pages/BidderPage/BidderPage.vue'
    import AuctionTemplatePage from '../../components/Auction/AuctionTemplatePage/AuctionTemplatePage.vue'
    import {bwp_initHomePage} from '../../services/bwp-connector/publisher'
    import AwardPage from '../../components/Auction/AwardPage/AwardPage.vue'
    import LoadingPage from '../../components/Pages/LoadingPage/LoadingPage.vue'
    import {WindowInstanceMap} from '../../helpers/window-instance'
    import PageLayout from '../../components/PageLayout/PageLayout.vue'

    @Component({
        provide() {
            return {
                [auStoreInjectKey]: this.store,
                [auConnectorInjectKey]: this.connector,
                [auLocalStoreInjectKey]: this.localStore,
            }
        },
        components: {
            PageLayout,
            LoadingPage,
            AwardPage,
            AuctionTemplatePage,
            BidderPage,
            UserPage,
            SessionsPage,
            HomePage,
            AuctionPage,
            LoginPage,
        },
    })
    export default class MainApp extends Vue {
        store: BwpModel = null
        connector: BwpConnector = null
        localStore: LocalStore = null
        longError: {title: string, body: string} = null

        constructor() {
            super()
            const store = new BwpStore()
            const connector = new BwpConnector(store)
            connector.connect()
            this.store = store
            this.connector = connector
            this.localStore = new LocalStore()
        }

        mounted() {
            // document.body.clientHeight for newly created page is 0. So we do a refresh here.
            // DM: nice!
            WindowInstanceMap.refresh()
        }

        get MrPage() {
            return MrPage
        }

        get next_page() {
            return this.store.next_page ? this.store.next_page.NEXT_PAGE : MrPage.LoginPage
        }

        get has_inited(): boolean {
            if (!this.store.next_page) {
                return true
            }
            return this.store.next_page.HAS_INITED
        }

        @Watch('store.alerts')
        on_alert(onAlerts: OnAlert[]) {
            onAlerts.forEach(onAlert => {
                const removeBlanks = (value: string): string => {
                    value = value.replace('↵', '\n') // Do proper line breaks
                    const matches = value.match(/^\s?\b([\s\S]*)$/) // Trim left
                    return matches ? matches[1] : value
                }
                let title = onAlert.TITLE ? removeBlanks(onAlert.TITLE) : ''
                let body = onAlert.BODY ? removeBlanks(onAlert.BODY) : ''
                if (!title) {
                    title = body
                    body = ''
                }
                const error = {
                    title,
                    body,
                }
                const errorIsLong = (error.title + error.body).length > 100
                // For long errors we use modal
                if (errorIsLong) {
                    this.longError = error
                    return
                }
                // For short errors notification is fine
                this.$notification.open({
                    description: error.body,
                    duration: 3,
                    message: error.title,
                    placement: 'topRight',
                })
            })
        }

        // Forces user to home page if auction is hidden.
        @Watch('store.forcing_home')
        onForcingHomeChange(value) {
            if (value) {
                bwp_initHomePage(this.connector)
            }
        }
    }
</script>

<style lang="less">
    .MainApp {
        height: 100%;
        width: 100%;

        &__container {
            height: 100%;
            max-width: 890px;
            min-width: 890px;
        }
    }

    .au-fade {
        &-enter-active {
            transition: all .1s ease;
        }

        &-leave-active {
            transition: all .1s ease;
        }

        &-enter, &-leave-to {
            opacity: 0.4;
        }
    }
</style>

