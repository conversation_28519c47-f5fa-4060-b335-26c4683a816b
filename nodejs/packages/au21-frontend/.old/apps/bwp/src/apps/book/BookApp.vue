<template>
  <router-view/>
</template>

<script lang="ts">
  import {createRoute, VueBookComponents} from 'vue-book'
  import {Component, Vue, Watch} from 'vue-property-decorator'
  import VueRouter from 'vue-router'
  import {auStoreInjectKey} from '../../plugins/store-plugin/store-plugin'
  import {auConnectorInjectKey} from '../../plugins/connector-plugin/connector-plugin'
  import {bwp_login} from '../../services/bwp-connector/publisher'
  import {BwpStore} from '../../store/store'
  import {BwpConnector} from '../../services/bwp-connector/connector'
  import {BwpModel} from '../../store/model'
  import {OnAlert} from '../../_generated/server_outputs'
  import {LocalStore} from '../../plugins/local-store-plugin/LocalStore'
  import {auLocalStoreInjectKey} from '../../plugins/local-store-plugin/local-store-plugin'
  import {sleep} from '../../services/utils'

  Vue.use(VueRouter)
Vue.use(VueBookComponents)

const router = new VueRouter({
  routes: [
    createRoute({
      requireContext: require.context('../..', true, /.demo.vue$/),
      path: '/',
      hideFileExtensions: true,
    }),
  ],
})

@Component({
  router,
  provide () {
    return {
      [auStoreInjectKey]: this.store,
      [auConnectorInjectKey]: this.connector,
      [auLocalStoreInjectKey]: this.localStore,
    }
  },
})
export default class BookApp extends Vue {
  store: BwpModel = null
  connector: BwpConnector = null
  localStore: LocalStore = null

  constructor () {
    super()
    const store = new BwpStore()
    const connector = new BwpConnector(store)
    connector.connect()
    this.store = store
    this.connector = connector
    this.localStore = new LocalStore()
  }

  async created () {
    await sleep(1000)
    bwp_login(this.connector, {
      username: 'b3',
      password: '1',
    })
  }

  @Watch('store.alerts')
  on_alert( onAlerts: OnAlert[] ){
    onAlerts.forEach(onAlert => {
      const removeBlanks = (value: string): string => {
        return value.replace("↵", '\n')
      }
      let title = onAlert.TITLE ? removeBlanks(onAlert.TITLE) : ''
      let body = onAlert.BODY ? removeBlanks(onAlert.BODY) : ''
      if (!title) {
        title = body
        body = ''
      }
      this.$notification.open({
        style: 'white-space: pre-wrap;',
        message: title,
        description: body,
      })
    })
  }
}
</script>
