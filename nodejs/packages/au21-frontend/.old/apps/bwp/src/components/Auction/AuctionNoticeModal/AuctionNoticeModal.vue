<template>
  <a-modal
    class="AuctionNoticeModal"
    title="Auction notice"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="800px"
    :maskClosable="false"
  >
    <div style="height: 500px" v-if="isEdit">
      <HtmlEditor v-model="html"/>
    </div>
    <div
      style="height: 500px"
      v-else
      ref="noticeModalContent"
      class="AuctionNoticeModal__html-container content ql-editor"
      v-html="html"
    />

    <a-button
      v-if="!isEdit && !readonly"
      slot="footer"
      @click="isEdit = true"
    >
      Edit
    </a-button>

    <a-button
      v-if="isEdit"
      slot="footer"
      @click="saveUpdatingNotice()"
    >
      Save
    </a-button>

    <a-button
      slot="footer"
      type="dashed"
      @click="$emit('close')"
    >
      Close
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import HtmlEditor from '../../../ui-components/HtmlEditor/HtmlEditor.vue'

@Component({
  components: { HtmlEditor },
})
export default class AuctionNoticeModal extends Vue {
  html = ''
  isEdit = false

  @Prop({ type: Boolean }) readonly: boolean
  @Prop({ required: true, type: String }) value: string

  @Watch('value', { immediate: true })
  onValueChange (value) {
    this.html = value
  }

  @Watch('value')
  async onNoticeUpdate () {
    await this.$nextTick()
    const fontElements: HTMLFontElement[] = Array.from((this.$refs.noticeModalContent as HTMLElement).querySelectorAll('font'))
    fontElements.forEach(fontElement => {
      // Fix font size
      const size = fontElement.getAttribute('size')
      if (size) {
        fontElement.setAttribute('style', `font-size: ${size}px`)
        fontElement.removeAttribute('size')
      }
      // Fix new lines
      if (!fontElement.innerText) {
        fontElement.innerHTML = '&nbsp;'
      }
    })
  }

  saveUpdatingNotice () {
    this.$emit('saveNotice', this.html)
    this.$emit('close')
  }
}
</script>

<style lang="less">
@import (reference) '../../../assets/variables.less';

.AuctionNoticeModal {
  .ant-modal-body {
    color: @au-text-color-secondary;
    padding: 0;
  }

  &__html-container {
    height: 500px;

    p {
      margin-bottom: 0;
    }

    overflow: auto;
    padding: 12px 15px;
    background-color: @au-pseudo-input-color;
  }
}
</style>
