<template>
  <VbDemo>
    <VbCard class="au-page">
      <AuctionClockLabelSelect style="width: 200px" v-model="value"/>
    </VbCard>
    <VbCard>
      {{ value }}
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuctionClockLabelSelect from './AuctionClockLabelSelect.vue'
import { MRClockLabel } from '../../../_generated/bwp-enums'

export default {
  components: {
    AuctionClockLabelSelect,
  },
  data () {
    return {
      value: 'PRICE' as MRClockLabel,
    }
  },
}
</script>
