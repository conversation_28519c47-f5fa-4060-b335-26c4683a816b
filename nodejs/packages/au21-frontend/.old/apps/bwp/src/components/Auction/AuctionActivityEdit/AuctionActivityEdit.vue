<template>
  <table class="AuctionActivityEdit">
    <tr style="text-align: center">
      <th>Activity ({{onMrAuctionSettings.VOLUME_LABEL}})</th>
      <th>Label</th>
      <th style="width: 40px"></th>
      <th>Change ({{onMrAuctionSettings.PRICE_LABEL}})</th>
    </tr>
    <tr>
      <td style="white-space: nowrap;">
        <OperatorSelect
          :disabled="displayOnly"
          v-model="onMrAuctionSettings.HIGH_OPERATOR"
        />

        <div class="pseudo-input AuctionActivityEdit__input" style="width: 100px;" v-if="displayOnly">
          {{onMrAuctionSettings.HIGH_LIMIT}}
        </div>
        <PriceInput
          v-else
          class="AuctionActivityEdit__input"
          style="width: 100px;"
          v-model="onMrAuctionSettings.HIGH_LIMIT"
        />
      </td>
      <td>
        <div class="pseudo-input AuctionActivityEdit__input" style="width: 40px;" v-if="displayOnly">
          {{onMrAuctionSettings.HIGH_LABEL}}
        </div>
        <a-input
          v-else
          class="AuctionActivityEdit__input"
          style="width: 40px"
          size="small"
          v-model="onMrAuctionSettings.HIGH_LABEL"
        />
      </td>
      <td/>
      <td>
        <div class="pseudo-input AuctionActivityEdit__input" v-if="displayOnly">
          {{onMrAuctionSettings.HIGH_CHANGE}}
        </div>
        <a-input
          class="AuctionActivityEdit__input"
          v-else
          size="small"
          v-model="onMrAuctionSettings.HIGH_CHANGE"
        />
      </td>
    </tr>
    <tr>
      <td>
        <OperatorSelect
          :disabled="displayOnly"
          style="white-space: nowrap;"
          v-model="onMrAuctionSettings.MED_OPERATOR"
        />
        <div class="pseudo-input AuctionActivityEdit__input" style="width: 100px;" v-if="displayOnly">
          {{onMrAuctionSettings.MED_LIMIT}}
        </div>
        <PriceInput
          v-else
          class="AuctionActivityEdit__input"
          style="width: 100px;"
          v-model="onMrAuctionSettings.MED_LIMIT"
        />
      </td>
      <td>
        <div class="pseudo-input AuctionActivityEdit__input" style="width: 40px;" v-if="displayOnly">
          {{onMrAuctionSettings.MED_LABEL}}
        </div>
        <a-input
          v-else
          class="AuctionActivityEdit__input"
          style="width: 40px"
          size="small"
          v-model="onMrAuctionSettings.MED_LABEL"
        />
      </td>
      <td/>
      <td>
        <div class="pseudo-input AuctionActivityEdit__input" v-if="displayOnly">
          {{onMrAuctionSettings.MED_CHANGE}}
        </div>
        <a-input
          v-else
          class="AuctionActivityEdit__input"
          size="small"
          v-model="onMrAuctionSettings.MED_CHANGE"
        />
      </td>
    </tr>
    <tr>
      <td>
      </td>
      <td>
        <div class="pseudo-input AuctionActivityEdit__input" style="width: 40px;" v-if="displayOnly">
          {{onMrAuctionSettings.LOW_LABEL}}
        </div>
        <a-input
          v-else
          class="AuctionActivityEdit__input"
          style="width: 40px"
          size="small"
          v-model="onMrAuctionSettings.LOW_LABEL"
        />
      </td>
      <td/>
      <td>
        <div class="pseudo-input AuctionActivityEdit__input" v-if="displayOnly">
          {{onMrAuctionSettings.LOW_CHANGE}}
        </div>
        <a-input
          v-else
          class="AuctionActivityEdit__input"
          size="small"
          v-model="onMrAuctionSettings.LOW_CHANGE"
        />
      </td>
    </tr>
  </table>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator'
import { OnMrAuctionSettings } from '../../../_generated/server_outputs'
import OperatorSelect from '../OperatorSelect/OperatorSelect.vue'
import PriceInput from '../../../ui-components/PriceInput/PriceInput.vue'

@Component({
  components: { PriceInput, OperatorSelect },
})
export default class AuctionActivityEdit extends Vue {
  @Prop({ required: true }) onMrAuctionSettings: OnMrAuctionSettings
  @Prop({ type: Boolean }) displayOnly: OnMrAuctionSettings
}
</script>

<style lang="less">
.AuctionActivityEdit {
  &__input {
    text-align: center;
    justify-content: center;
    width: 100%;
  }

  td, th {
    padding: 5px;
  }
}
</style>
