<template>
  <div class="AuctionPage">
    <div class="form-block">
      <div style="display: flex; align-items: center;">
        <div class="AuctionPage__header-label">Auction:</div>
        <div class="pseudo-input" style="height: 36px; overflow-y: hidden">
          {{ $auStore.current_auction.status.AUCTION_NAME }}
        </div>
      </div>
      <div style="display: flex; align-items: center;">
        <div class="AuctionPage__header-label">Status:</div>
        <div class="pseudo-input AuctionPage__header-text auction-status" style="height: 24px">
          {{ $auStore.current_auction.status.AUCTION_STATUS }}
        </div>
      </div>
    </div>

    <div style="display: flex; align-items: stretch">
      <div style="flex: 1 1" class="form-block">
        <AuctionFlowController
            :mrState="$auStore.current_auction.status.ROUND_CONTROLLER_STATUS"
            :clock_label="mrClockLabel"
            @restart="restart()"
            @showSetPrice="showSetPriceModal = true"
            @go="go()"
            @pause="pause()"
            @endAuction="endAuction()"
            @next="next()"
        />
        <SetRoundPriceModal
            v-if="showSetPriceModal"
            @close="showSetPriceModal = false"
            :onMrAuctionStatus="$auStore.current_auction.status"
            :clock_label="mrClockLabel"
            @saveSettingPrice="saveSettingPrice"
        />
      </div>
      <div style="flex: 1 1" class="form-block">
        <AuctioneerToolbar
            @editNotice="editNotice()"
            @showSettings="showSettings()"
            @award="award()"
        />
        <AuctionNoticeModal
            :value="$auStore.current_auction.notice"
            v-if="showAuctionNotice"
            @saveNotice="saveNotice"
            @close="showAuctionNotice = false"
        />
        <AuctionSettingsModal
            v-if="showAuctionSettingsModal && $auStore.current_auction.settings"
            :onMrAuctionSettings="$auStore.current_auction.settings"
            @saveAuction="saveAuction"
            @saveAsTemplate="saveAuctionAsTemplate()"
            @close="showAuctionSettingsModal = false"
        />
      </div>
    </div>
    <div class="form-block">
      <AuctioneerAuctionStatusBar
          :onMrAuctioneerSummary="$auStore.current_auction.auctioneer_summary"
          :biddersTotal="biddersTotal"
          :biddersOnline="biddersOnline"
          :hideNonBidders.sync="hideNonBiddersProxy"
      />
    </div>
    <div class="form-block" style="display: flex">
      <AuctionRoundBlotter
          ref="AuctionRoundBlotter"
          :height="tableHeight"
          :width="600"
          :onMrBidderBlotterRowList="onMrBidderBlotterRowListComputed"
          :blotterHeaderRows="$auStore.current_auction.blotter_header_rows"
          @setEligibility="onMrBidderBlotterRowEdited = $event"
          :sortDirection.sync="sortDirection"
          :sortBy.sync="sortBy"
      />
      <BidderEligibilityUpdateModal
          v-if="onMrBidderBlotterRowEdited"
          @close="onMrBidderBlotterRowEdited = null"
          :onMrBidderBlotterRow="onMrBidderBlotterRowEdited"
          @saveBidderEligibilityUpdate="saveBidderEligibilityUpdate"
      />
      <div
          style="display: flex; flex-direction: column; padding-left: 4px"
          :style="{
                  width: $auLocalStore.config.width_inner - 598 + 'px',
                  height: tableHeight + 'px',
                  maxHeight: tableHeight + 'px',
                }"
      >
        <div class="text-bold text-center">Auction messages</div>
        <a-textarea
            v-model="chatMessage"
            class="AuctionPage__chat"
            :autosize="{ minRows: 2, maxRows: 12 }"
            @pressEnter="submitChatMessage"
        />
        <AuctionMessageList
            style="flex: 1 1"
            sender="Auctioneer"
            :onMessageList="messageList"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {Component, Vue, Watch} from 'vue-property-decorator'
import PageLayout from '../../PageLayout/PageLayout.vue'
import AuctionFlowController
  from '../AuctionFlowController/AuctionFlowController.vue'
import AuctioneerToolbar from '../AuctioneerToolbar/AuctioneerToolbar.vue'
import AuctioneerAuctionStatusBar
  from '../AuctioneerAuctionStatusBar/AuctioneerAuctionStatusBar.vue'
import AuctionMessageList
  from '../../AuctionMessages/AuctionMessageList/AuctionMessageList.vue'
import AuctionRoundBlotter from '../AuctionRoundBlotter/AuctionRoundBlotter.vue'
import {
  OnMessage,
  OnMrBidderBlotterRow, OnMrAuctionSettings,
} from '../../../_generated/server_outputs'
import SetRoundPriceModal from '../SetRoundPriceModal/SetRoundPriceModal.vue'
import {
  bwp_setPrice,
  bwp_command,
  bwp_initAwardPage,
  bwp_createMessage,
  bwp_setEligibility,
  bwp_getNotice,
  bwp_setNotice,
  bwp_getSettings,
  bwp_saveSettings, bwp_saveTemplate,
} from '../../../services/bwp-connector/publisher'
import {MRClockLabel, MRCommand} from '../../../_generated/bwp-enums'
import BidderEligibilityUpdateModal
  from '../BidderEligibilityUpdateModal/BidderEligibilityUpdateModal.vue'
import AuctionNoticeModal from '../AuctionNoticeModal/AuctionNoticeModal.vue'
import AuctionEdit from '../AuctionEdit.vue'
import AuctionSettingsModal from './AuctionSettingsModal.vue'
import {MAX_HEIGHT} from '../../../helpers/height_helper'
import {sortWithDirection} from '../../../helpers/array-helpers'
import {DebounceLoader} from 'asva-executors'
import {getMrClockLabelName} from "../__demo-helpers/MrClockLabel";

@Component({
  components: {
    AuctionSettingsModal,
    AuctionEdit,
    AuctionNoticeModal,
    BidderEligibilityUpdateModal,
    SetRoundPriceModal,
    AuctionRoundBlotter,
    AuctionMessageList,
    AuctioneerAuctionStatusBar,
    AuctioneerToolbar,
    AuctionFlowController,
    PageLayout,
  },
})
export default class AuctionPage extends Vue {
  showAuctionNotice = false

  showAuctionSettingsModal = false
  editedAuctionSettings = null

  sortBy: 'USERNAME' | 'ELIGIBILITY_NUM' = 'USERNAME'
  sortDirection: 'asc' | 'desc' = 'asc'

  showSetPriceModal = false
  chatMessage = ''
  onMrBidderBlotterRowEdited: OnMrBidderBlotterRow = null

  // Prevents table from being redrawn on each update (backend might send hundreds of these depending on list size).
  onMrBidderBlotterRowListDebounced: OnMrBidderBlotterRow[] = []
  onMrBidderBlotterRowDebounceLoader = new DebounceLoader(async () => {
    this.refreshDebounceLoaderRow()
  }, 100)

  get mrClockLabel() {
    return getMrClockLabelName(this.$auStore.current_auction.settings.CLOCK_LABEL || MRClockLabel.PRICE)
  }

  @Watch('blotterBidderRows', {deep: true, immediate: true})
  onBlotterBidderRowsChange() {
    this.onMrBidderBlotterRowDebounceLoader.run()
  }

  refreshDebounceLoaderRow() {
    this.onMrBidderBlotterRowListDebounced = [...this.blotterBidderRows]
  }

  get blotterBidderRows() {
    return this.$auStore.current_auction.blotter_bidder_rows
  }

  get hideNonBiddersProxy() {
    return this.$auLocalStore.config.hide_non_bidders
  }

  set hideNonBiddersProxy(value: boolean) {
    this.$auLocalStore.config.hide_non_bidders = value
  }

  @Watch('roundNumber')
  onRoundNumberChange(value) {
    (this.$refs.AuctionRoundBlotter as any).scrollToRound(this.roundNumber)
  }

  get roundNumber() {
    return this.$auStore.current_auction.status.ROUND_NUMBER
  }

  get messageList() {
    return this.$auStore.current_auction.messages
  }

  submitChatMessage(event: KeyboardEvent) {
    if (event.ctrlKey || event.shiftKey) {
      return
    }
    bwp_createMessage(this.$auConnector, this.chatMessage)
    this.chatMessage = ''
    event.preventDefault()
  }

  get biddersTotal() {
    return this.$auStore.current_auction.blotter_bidder_rows.length
  }

  get biddersOnline() {
    return this.$auStore.current_auction.blotter_bidder_rows
        .filter(onMrBidderRow => onMrBidderRow.IS_ONLINE)
        .length
  }

  get onMrBidderBlotterRowListComputed() {
    let bidders = this.onMrBidderBlotterRowListDebounced

    if (this.hideNonBiddersProxy) {
      bidders = bidders.filter(row => row.HAS_SEEN_AUCTION)
    }

    return sortWithDirection(bidders, this.sortBy, this.sortDirection)
  }

  get tableHeight() {
    if (this.$auLocalStore.config.height_inner <= MAX_HEIGHT) {
      return MAX_HEIGHT - 135
    }

    return this.$auLocalStore.config.height_inner - 125
  }

  get auction_messages(): OnMessage[] {
    return this.$auStore.current_auction.messages
  }

  showSettings() {
    bwp_getSettings(this.$auConnector)
    this.showAuctionSettingsModal = true
  }

  saveAuction(onMrAuctionSettings: OnMrAuctionSettings) {
    bwp_saveSettings(this.$auConnector, onMrAuctionSettings)
    this.showAuctionSettingsModal = false
  }

  saveAuctionAsTemplate() {
    bwp_saveTemplate(this.$auConnector)
  }

  saveBidderEligibilityUpdate(eligibility) {
    this.onMrBidderBlotterRowEdited
    // User id is called OID for blotter row for some reason.
    bwp_setEligibility(this.$auConnector, this.onMrBidderBlotterRowEdited.OID, eligibility)
    this.onMrBidderBlotterRowEdited = null
  }

  editNotice() {
    this.editedAuctionSettings = null
    bwp_getNotice(this.$auConnector)
    this.showAuctionNotice = true
  }

  saveNotice(html) {
    bwp_setNotice(this.$auConnector, html)
  }

  restart() {
    bwp_command(this.$auConnector, MRCommand.RESTART_ROUND)
  }

  saveSettingPrice(price) {
    bwp_setPrice(this.$auConnector, price)
  }

  go() {
    bwp_command(this.$auConnector, MRCommand.GO)
  }

  pause() {
    bwp_command(this.$auConnector, MRCommand.PAUSE)
  }

  endAuction() {
    bwp_command(this.$auConnector, MRCommand.END_ROUND)
  }

  next() {
    bwp_command(this.$auConnector, MRCommand.NEXT_ROUND)
  }

  award() {
    bwp_initAwardPage(this.$auConnector)
  }
}
</script>

<style lang="less">
.AuctionPage {
  display: flex;
  font-size: 12px;
  flex-direction: column;

  &__header-label {
    flex: 0 0 60px;
    text-align: right;
    margin-right: 6px;
  }

  & &__chat {
    resize: none;
    margin-top: 4px;
    margin-bottom: 4px
  }
}

.auction-status {
  font-weight: bold;
  font-size: 14px;
}
</style>
