<template>
  <VbDemo>
    <VbCard class="au-page">
      <AuctionFlowController
        style="width: 500px"
        :mrState="mrState"
        @restart="log.info('restart')"
        @showSetPrice="log.info('showSetPrice')"
        @go="log.info('go')"
        @pause="log.info('pause')"
        @endAuction="log.info('endAuction')"
        @next="log.info('next')"
      />
    </VbCard>
    <VbCard>
      <a-select v-model="mrState">
        <a-select-option
          v-for="option in mrStateOptions"
          :key="option"
          :value="option"
        >
          {{option}}
        </a-select-option>
      </a-select>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import { MRState } from '../../../_generated/bwp-enums'
import AuctionFlowController from './AuctionFlowController.vue'
import { $enum } from 'ts-enum-util'
import { LogMixin } from '../../Login/logMixin'

@Component({
  components: { AuctionFlowController },
  mixins: [LogMixin],
})
export default class AuctionFlowControllerDemo extends Vue {
  mrState = MRState.AUCTION_CLOSED
  mrStateOptions = $enum(MRState).getKeys()
}
</script>
