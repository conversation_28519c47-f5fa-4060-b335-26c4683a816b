<template>
  <a-modal
    class="AuctionSettingsModal"title="Auction settings"
    @cancel="$emit('close')"
    visible
    closable
    centered
    width="900px"
  >
    <AuctionDisplay v-if="!isEdit" :onMrAuctionSettings="editedAuctionSettings"/>
    <AuctionEdit v-else :onMrAuctionSettings="editedAuctionSettings"/>

    <a-button
      v-if="!readonly && !isEdit"
      slot="footer"
      @click="isEdit = true"
    >
      Edit
    </a-button>

    <a-button
      v-if="isEdit"
      slot="footer"
      @click="$emit('saveAuction', editedAuctionSettings)"
    >
      Save
    </a-button>

    <a-button
      slot="footer"
      @click="$emit('saveAsTemplate')"
    >
      Save as template
    </a-button>

    <a-button
      slot="footer"
      type="dashed"
      @click="$emit('close')"
    >
      Close
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator'
import AuctionEdit from '../AuctionEdit.vue'
import { OnMrAuctionSettings } from '../../../_generated/server_outputs'
import AuctionDisplay from '../AuctionDisplay.vue'

@Component({
  components: { AuctionDisplay, AuctionEdit },
})
export default class AuctionSettingsModal extends Vue {
  isEdit = false
  editedAuctionSettings: OnMrAuctionSettings = null
  @Prop({ required: true }) onMrAuctionSettings: OnMrAuctionSettings
  @Prop({ type: Boolean }) readonly: boolean

  @Watch('onMrAuctionSettings', {immediate: true})
  onMrAuctionSettingsChange (onMrAuctionSettings) {
    this.editedAuctionSettings = Object.assign(new OnMrAuctionSettings(), onMrAuctionSettings)
  }
}
</script>

<style lang="less">
.AuctionSettingsModal {

}
</style>
