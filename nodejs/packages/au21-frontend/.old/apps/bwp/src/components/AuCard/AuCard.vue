<template>
  <div class="AuCard">
    <slot/>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({})
export default class AuCard extends Vue {

}
</script>

<style lang="less">
@import '../../assets/variables.less';

.AuCard {
  padding: @au-padding-large;
  background-color: @au-background;
  border-radius: @au-border-radius;
  color: @au-text-color;
}
</style>
