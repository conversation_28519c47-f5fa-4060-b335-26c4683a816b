/**
 * Ant design doesn't have fixed rows.
 * And we really really need them.
 * So we create nested headers instead.
 *
 * `titles` should have at least 2 items.
 */

export const createNestedColumn = (column: any, titles: string[], fixed: string = null) => {
  const titlesLocal = [...titles]
  const title = titlesLocal.shift()
  const result: any = { title }

  if (fixed) {
    result.fixed = fixed
  }

  if (!titlesLocal.length) {
    return Object.assign(result, column)
  }

  result.children = [createNestedColumn(column, titlesLocal)]

  return result
}
