<template>
  <VbDemo>
    <VbCard>
      <AddBidderModal
        v-if="show"
        :isRemove="isRemove"
        @close="show = false"
      />
    </VbCard>
    <VbCard>
      <a-button @click="show = true">Show</a-button>
    </VbCard>
    <VbCard>
      <a-checkbox v-model="isRemove">Is remove</a-checkbox>
    </VbCard>

  </VbDemo>
</template>

<script lang="ts">
import AddBidderModal from './AddBidderModal.vue'

export default {
  components: {
    AddBidderModal,
  },
  data () {
    return {
      show: false,
      isRemove: false,
    }
  },
}
</script>
