<template>
  <VbDemo>
    <VbCard class="au-page">
      <AuctionActivityEdit :onMrAuctionSettings="onMrAuctionSettings"/>
    </VbCard>
    <VbCard>
      <pre>{{onMrAuctionSettings}}</pre>
    </VbCard>
  </VbDemo>
</template>

<script>
import AuctionActivityEdit from './AuctionActivityEdit.vue'
import { createOnMrAutionSettingsForEdit } from '../__demo-helpers/OnMrAuctionSettings'

export default {
  components: {
    AuctionActivityEdit,
  },
  data () {
    return {
      onMrAuctionSettings: createOnMrAutionSettingsForEdit(),
    }
  },
}
</script>
