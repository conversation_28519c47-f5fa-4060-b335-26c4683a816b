<template>
  <VbDemo>
    <VbCard>
      <AuctionRoundBlotter
        ref="AuctionRoundBlotter"
        :width="594"
        :height="610"
        :blotterHeaderRows="blotterHeaderRows"
        :onMrBidderBlotterRowList="onMrBidderBlotterRowList"
        @setEligibility="log.info('setEligibility', $event)"
        sortBy="ELIGIBILITY"
      />
    </VbCard>
    <VbCard title="Scroll to round">
      <button @click="$refs.AuctionRoundBlotter.scrollToRound(5)">Scroll to 5th round</button>
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AuctionRoundBlotter from './AuctionRoundBlotter.vue'
import { createMultipleByClosure } from '../../../helpers/array-helpers'
import { createOnMrBidderBlotterRow } from '../__demo-helpers/OnMrBidderBlotterRow'
import { LogMixin } from '../../Login/logMixin'
import { createBlotterHeaderRows } from '../__demo-helpers/MrBlotterFixedRow'

@Component({
  components: { AuctionRoundBlotter },
  mixins: [LogMixin],
})
export default class AuctionRoundBlotterDemo extends Vue {
  onMrBidderBlotterRowList = createMultipleByClosure(createOnMrBidderBlotterRow, 30)
  blotterHeaderRows = createBlotterHeaderRows()
}
</script>
