<template>
  <svg class="BidderOnlineIcon" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
       width="16px" height="16px" viewBox="0 0 466.146 466.146" style="enable-background:new 0 0 466.146 466.146;"
       xml:space="preserve">
    <g>
      <path d="M289.285,191.86c28.844-18.539,47.995-50.83,47.995-87.654C337.28,46.659,290.621,0,233.088,0
        c-57.559,0-104.207,46.659-104.207,104.207c0,36.824,19.151,69.121,47.996,87.654c-67.959,6.082-121.422,63.331-121.422,132.854
        v108.155l0.274,1.69l7.457,2.328c70.196,21.929,131.195,29.259,181.401,29.259c98.048,0,154.886-27.97,158.408-29.743l6.963-3.534
        h0.732V324.714C410.698,255.197,357.253,197.957,289.285,191.86z"/>
    </g>
  </svg>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'

@Component({})
export default class BidderOnlineIcon extends Vue {

}
</script>

<style lang="less">
.BidderOnlineIcon {
  fill: #5a9c4b;
  margin-bottom: -4px;
}
</style>
