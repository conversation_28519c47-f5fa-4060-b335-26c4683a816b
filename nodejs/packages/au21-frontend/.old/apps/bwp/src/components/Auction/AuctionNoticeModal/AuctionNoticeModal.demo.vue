<template>
  <VbDemo>
    <VbCard>
      <a-button @click="show = true">Show</a-button>
      <AuctionNoticeModal
        :value="html"
        @saveNotice="$vb.log('saveNotice', $event)"
        @close="show = false"
        v-if="show"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import AuctionNoticeModal from './AuctionNoticeModal.vue'
import { Component, Vue } from 'vue-property-decorator'
import { LogMixin } from '../../Login/logMixin'

@Component({
  components: { AuctionNoticeModal },
})
export default class AuctionNoticeModalDemo extends Vue {
  show = false
  html =`<p :onclick="alert("XSS success!");">Dangerous button</p>`
}
</script>
