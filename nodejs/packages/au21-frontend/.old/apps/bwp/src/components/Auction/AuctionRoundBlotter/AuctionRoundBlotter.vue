<template>
  <div class="AuctionRoundBlotter">
    <div
        class="AuctionRoundBlotter__hacky-colspan-overlay"
        :style="{
            width: fixed_columns[0].width + fixed_columns[1].width + 'px',
            height: header_height * 4 + 'px'
          }"
    >
      <div
          :style="{height: header_height + 'px', 'justify-content': 'flex-end'}"
          class="flex-center"
      >
        Round:
      </div>
      <div
          v-for="index in [1,2,3]"
          :style="{height: header_height + 'px', 'justify-content': 'flex-end'}"
          class="flex-center"
      >
        {{ header_rows[index].label }}:
      </div>
    </div>

    <AuScrollableTableStepped
        ref="AuScrollableTableStepped"
        :cell_height="cell_height"
        :columns="columns"
        :columnWidthSetter="column => column.width"
        :first_column_width="first_column_width"
        :fixed_columns="fixed_columns"
        :header_height="header_height"
        :header_rows="header_rows"
        :rows="onMrBidderBlotterRowList"
        :table_height="height"
        :table_width="width"
        :visible_columns="visible_columns"
    >
      <!-- fixed HEADERs -->

      <template
          slot="fixed_header_cell"
          slot-scope="{column, column_index, row, row_index}"
      >
        <div
            v-if="row_index !== 4"
            style="width: 100%; height: 100%; background-color: #333;"
        />

        <div
            v-else-if="row_index === 4 && column_index === 0"
            class="table-cell-wrapper"
            style="text-align: left; padding: 2px; background-color: #444; color: white; cursor: pointer"
            @click="onSort('USERNAME')"
        >
          <span style="width: 18px; display: inline-block; "/>
          {{ row.label }}
          <a-icon v-if="sortByProxy === 'USERNAME'" style="width: 12px; height: 12px"
                  :type="sortDirectionProxy === 'asc' ? 'down' : 'up'"/>
        </div>
        <div
            v-else-if="row_index === 4 && column_index === 1"
            class="table-cell-wrapper"
            style="text-align: center; padding: 2px; background-color: #444; color: white; cursor: pointer"
            @click="onSort('ELIGIBILITY_NUM')"
        >
          Eligibility
          <a-icon v-if="sortByProxy === 'ELIGIBILITY_NUM'" style="width: 12px; height: 12px"
                  :type="sortDirectionProxy === 'asc' ? 'down' : 'up'"/>
        </div>

        <div
            v-if="column_index === 0"
            class="fill-container"
            style="padding: 2px; text-align: left; background-color: #555; color: white"
        >
          <span style="width: 18px; display: inline-block;"/> {{ row.label }}
        </div>
      </template>

      <template
          slot="fixed_body_cell"
          slot-scope="{row, column, column_index}"
      >
        <div
            v-if="column_index === 0"
            class="fill-container"
            style="padding: 2px; text-align: left; background-color: #555; color: white;"
        >
                <span style="width: 18px; display: inline-block">
                  <BidderOnlineIcon v-if="row.IS_ONLINE"/>
                </span>
          <Blinker :value="row.IS_ONLINE">{{ row.USERNAME }}</Blinker>
        </div>
        <div
            v-else
            class="fill-container"
            style="padding: 2px; cursor: pointer; background-color: #555; color: white;"
            @click="$emit('setEligibility', row)"
        >
          <Blinker class="text-link" style="color: #94b6ff" :value="row.ELIGIBILITY"/>
        </div>
      </template>


      <!-- scrollable column -->
      <template
          slot="scrolling_header_cell"
          slot-scope="{column, row, row_index}"
      >
        <div
            v-if="row_index === 0"
            class="table-cell-wrapper"
            style="text-align: right; padding: 2px; background-color: #333; color: white;"
        >
          {{ row_index === 0 ? column.round : row[`Round_${column.round}`] }}
        </div>
        <div
            v-if="row_index === 4"
            class="table-cell-wrapper"
            style="text-align: center; padding: 2px; background-color: #444; color: white;"
        >
          {{ row_index === 0 ? column.round : row[`Round_${column.round}`] }}
        </div>
        <div
            v-else
            class="fill-container"
            :style="{
          'padding': '2px',
          'text-align': row_index === 0 && 'left',
        }"
        >
          {{ row[`Round_${column.round}`] }}
        </div>
      </template>

      <div
          slot="scrolling_body_cell"
          slot-scope="{row, column}"
          class="fill-container"
          style="padding: 2px"
      >
        <Blinker :value="row[`Round_${column.round}`]"/>
      </div>
    </AuScrollableTableStepped>
  </div>
</template>

<script lang="ts">
import {Component, Prop, Vue, Watch} from 'vue-property-decorator'
import {OnMrBidderBlotterRow} from '../../../_generated/server_outputs'
import {createMultipleByClosure} from '../../../helpers/array-helpers'
import {Column, Row} from '../../../ui-components/TableScroller/helpers'
import AuScrollableTableStepped
  from '../../../ui-components/TableScroller/AuScrollableTableStepped.vue'
import Blinker from '../../../ui-components/Blinker/Blinker.vue'
import {MrBlotterFixedRow} from '../../../store/model'
import BidderOnlineIcon from './BidderOnlineIcon.vue'
import {
  isBrowserIE,
  isBrowserEdge,
} from '../../../helpers/browser-helpers'

@Component({
  components: {
    BidderOnlineIcon,
    Blinker,
    AuScrollableTableStepped,
  },
})
export default class AuctionRoundBlotter extends Vue {
  @Prop({required: true, type: Number}) height: number
  @Prop({required: true, type: Number}) width: number
  @Prop({required: true}) onMrBidderBlotterRowList: OnMrBidderBlotterRow[]
  @Prop({required: true}) blotterHeaderRows

  // -
  @Prop({required: true, type: String}) sortDirection: 'asc' | 'desc'

  get sortDirectionProxy() {
    return this.sortDirection
  }

  set sortDirectionProxy(sortDirection) {
    this.$emit('update:sortDirection', sortDirection)
  }

  // -
  @Prop({required: true, type: String}) sortBy: 'ELIGIBILITY_NUM' | 'USERNAME'

  get sortByProxy() {
    return this.sortBy
  }

  set sortByProxy(sortBy) {
    this.$emit('update:sortBy', sortBy)
  }

  onSort(sortBy: 'ELIGIBILITY_NUM' | 'USERNAME') {
    if (sortBy === this.sortByProxy && this.sortDirectionProxy === 'asc') {
      this.sortDirectionProxy = 'desc'
      return
    }
    this.sortByProxy = sortBy
    this.sortDirectionProxy = 'asc'
  }

  header_height = 24
  cell_height = 24
  visible_columns = 4
  round_column_width = 176
  eligibility_column_width = 80

  get first_column_width(): number {
    return this.round_column_width + this.eligibility_column_width
  }

  scrollToRound(roundNumber: number): void {
    setTimeout(() => {
      (this.$refs.AuScrollableTableStepped as AuScrollableTableStepped).scrollToColumn(roundNumber)
      // Wait until bidder appears.
    }, 200)
    return
  }

  get topRow() {
    let topRow = new MrBlotterFixedRow()
    topRow.label = 'topRow'

    Object.keys(topRow).forEach(key => {
      if (topRow[key]) {
        return
      }
      // Round is active if it has value for at least one bidder
      if (this.onMrBidderBlotterRowList.some(
          onMrBidderBlotterRow => onMrBidderBlotterRow[key]
      )) {
        topRow[key] = '+'
      }
    })

    // If no bidders are added - we'll still display 4 rounds so that formatting looks ok.
    if (!this.onMrBidderBlotterRowList.length) {
      topRow['Round_1'] = '+'
      topRow['Round_2'] = '+'
      topRow['Round_3'] = '+'
      topRow['Round_4'] = '+'
    }

    const newTopRow: Partial<MrBlotterFixedRow> = {}
    Object.keys(topRow).forEach(key => {
      if (topRow[key]) {
        newTopRow[key] = topRow[key]
      }
    })

    return newTopRow
  }

  get header_rows() {
    return [
      this.topRow,
      this.blotterHeaderRows.ROUNDPRICE,
      this.blotterHeaderRows.TOTALVOL,
      this.blotterHeaderRows.ACTIVITY,
      this.blotterHeaderRows.BIDDERS,
    ] as MrBlotterFixedRow[]
  }

  get fixed_columns() {
    return [
      {
        key: 'Col1',
        text: 'Round',
        width: 176,
      },
      {
        key: 'ELIGIBILITY',
        text: 'Eligibility',
        width: 80,
      },
    ]
  }

  get rows(): Row<string>[] {
    return createMultipleByClosure(index => ({
      id: index + '',
      USERNAME: 'Trader ' + index,
      ELIGIBILITY: index * 10 + ',000',
    }), 100)
  }

  get columns(): Column<string>[] {
    const columns = Object.keys(this.topRow)
        .filter(key => key !== 'label')
        .map(key => {
          const index = Number(key.split('_')[1])
          return {
            field: 'ROUND_' + index, // ROUND_1
            label: key, // Round_1
            round: index, // 1
          }
        })


    const maxRows = 4
    if (columns.length < maxRows) {
      const difference = maxRows - columns.length
      Array(difference).fill(null).map((value, index) => {
        const count = maxRows - difference + index + 1
        columns[count - 1] = {
          field: `ROUND_${count}`, // ROUND_1
          label: `Round_${count}`, // Round_1
          round: count, // 1
        }
      })
    }

    return columns
  }
}
</script>

<style lang="less">
@import (reference) '../../../assets/variables.less';

.AuctionRoundBlotter {
  position: relative;

  &__hacky-colspan-overlay {
    left: 2px;
    top: 2px;
    padding-right: 2px;
    text-align: right;
    z-index: 1;
    position: absolute;
    background-color: @au-background;
  }
}
</style>
