<template>
  <div v-if="disabled" class="pseudo-input">
    {{ selectedValueText }}
  </div>
  <a-select
      v-else
      class="AuctionClockLabelSelect"
      size="small"
      defaultValue="lucy"
      style="width: 120px"
      v-model="valueProxy"
  >
    <a-select-option
        v-for="option in options"
        :key="option.value"
        :value="option.value"
    >
      {{option.name}}
    </a-select-option>
  </a-select>
</template>

<script lang="ts">
import {Component, Prop, Vue} from 'vue-property-decorator'
import {MRClockLabel} from '../../../_generated/bwp-enums'
import {getMrClockLabelName, getMrClockLabelOptions} from "../__demo-helpers/MrClockLabel";

@Component({})
export default class AuctionClockLabelSelect extends Vue {
  @Prop({type: String}) value: MRClockLabel
  @Prop({type: Boolean}) disabled: boolean

  options = getMrClockLabelOptions()
  
  get valueProxy(): MRClockLabel {
    return this.value
  }

  set valueProxy(value) {
    this.$emit('input', value)
  }

  get selectedValueText() {
    return getMrClockLabelName(this.value)
  }
}
</script>

<style lang="less">
.AuctionClockLabelSelect {

}
</style>
