<template>
  <a-modal
    class="AddBidderModal"
    :title="operationTitle"
    @cancel="cancelAddingBidders()"
    visible
    closable
    centered
    width="700px"
  >
    <BidderSelectTable
      :style="{height: '500px'}"
      :height="500"
      :bidderList="localBidderList"
      v-model="selectedUserIds"
    />

    <a-button
      slot="footer"
      @click="saveAddingBidders()"
    >
      {{operationTitle}}
    </a-button>

    <a-button
      slot="footer"
      type="dashed"
      @click="cancelAddingBidders()"
    >
      Cancel
    </a-button>
  </a-modal>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator'
import BidderSelectTable from '../BidderSelectTable/BidderSelectTable.vue'
import {
  bpw_getAvailableUsers,
  bwp_addUsers,
  bwp_getCurrentUsers,
  bwp_removeUsers,
} from '../../../services/bwp-connector/publisher'
import { DebounceLoader } from 'asva-executors'
import { OnAuctionUserRow } from '../../../_generated/server_outputs'

@Component({
  components: { BidderSelectTable },
})
export default class AddBidderModal extends Vue {
  selectedUserIds = []
  @Prop({ type: Boolean }) isRemove
  
  // Required to prevent table redraws and hanging thread on sequential bidder addition.
  localBidderList: OnAuctionUserRow[] = []
  debounceLoader: DebounceLoader
  
  constructor() {
    super()
    this.debounceLoader = new DebounceLoader(async () => this.refreshLocalBidderList(), 100)
  }
  
  created () {
    if (this.isRemove) {
      bwp_getCurrentUsers(this.$auConnector)
    } else {
      bpw_getAvailableUsers(this.$auConnector)
    }
  }

  saveAddingBidders () {
    if (this.isRemove) {
      bwp_removeUsers(this.$auConnector, this.selectedUserIds)
    } else {
      bwp_addUsers(this.$auConnector, this.selectedUserIds)
    }
    this.$emit('close')
  }

  cancelAddingBidders () {
    this.$emit('close')
  }

  refreshLocalBidderList () {
    this.localBidderList = [...this.bidderList]
  }
  
  @Watch('bidderList')
  onBidderListChange(bidderList) {
    this.debounceLoader.run()
    return bidderList
  }
  
  get bidderList () {
    return this.$auStore.traders_add_remove.traders
  }

  get operationTitle () {
    return (this.isRemove ? 'Remove' : 'Add') + ' bidders'
  }
}
</script>

<style lang="less">
.AddBidderModal {
  .ant-modal-body {
    padding: 0;
  }
}
</style>
