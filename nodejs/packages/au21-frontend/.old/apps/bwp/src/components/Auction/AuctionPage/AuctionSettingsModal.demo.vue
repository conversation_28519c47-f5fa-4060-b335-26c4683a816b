<template>
  <VbDemo>
    <VbCard>
      <a-button @click="show = true">Show modal</a-button>
      <AuctionSettingsModal
        v-if="show"
        :onMrAuctionSettings="onMrAuctionSettings"
        @saveAuction="$vb.log('saveAuction', $event)"
        @saveAsTemplate="$vb.log('saveAsTemplate')"
        @close="show = false"
      />
    </VbCard>
  </VbDemo>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator'
import AuctionSettingsModal from './AuctionSettingsModal.vue'
import { createOnMrAutionSettingsForEdit } from '../__demo-helpers/OnMrAuctionSettings'

@Component({
  components: { AuctionSettingsModal },
})
export default class AuctionSettingsModalDemo extends Vue {
  onMrAuctionSettings = createOnMrAutionSettingsForEdit()
  show = false
}
</script>
