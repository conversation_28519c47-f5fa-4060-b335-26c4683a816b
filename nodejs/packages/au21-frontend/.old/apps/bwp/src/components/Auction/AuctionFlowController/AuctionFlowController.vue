<template>
  <div class="AuctionFlowController">
    <div class="mr-1" style="flex: 0 0 60px; text-align: right">Round:</div>
    <a-button
      class="AuctionFlowController__button"
      :disabled="!isRestartEnabled"
      @click="$emit('restart')"
      size="small"
    >
      Restart
    </a-button>
    <a-button
      class="AuctionFlowController__button"
      @click="$emit('showSetPrice')"
      :disabled="!isSetPriceEnabled"
      size="small"
      style="flex: 0 0 50px;"
    >
      Set {{ clock_label }}
    </a-button>
    <a-button
      class="AuctionFlowController__button"
      @click="$emit('go')"
      :disabled="!isGoEnabled"
      size="small"
    >
      Go
    </a-button>
    <a-button
      class="AuctionFlowController__button"
      @click="$emit('pause')"
      :disabled="!isPauseEnabled"
      size="small"
    >
      Pause
    </a-button>
    <a-button
      class="AuctionFlowController__button"
      @click="$emit('endAuction')"
      :disabled="!isEndEnabled"
      size="small"
    >
      End
    </a-button>
    <a-button
      class="AuctionFlowController__button"
      @click="next()"
      :disabled="!isNextEnabled"
      size="small"
    >
      Next
    </a-button>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator'
import SetRoundPriceModal from '../SetRoundPriceModal/SetRoundPriceModal.vue'
import { MRState } from '../../../_generated/bwp-enums'

@Component({
  components: { SetRoundPriceModal },
})
export default class AuctionFlowController extends Vue {
  showSetPrice = false
  @Prop({ required: true }) mrState: MRState
  @Prop({ required: true}) clock_label:string

  get isRestartEnabled (): boolean {
    const isRestartEnabledMap = {
      [MRState.ROUND_INIT]: false,
      [MRState.ROUND_PRICE_SET]: false,
      [MRState.ROUND_CLOCK_RUNNING]: false,
      [MRState.ROUND_CLOCK_PAUSED]: false,
      [MRState.ROUND_CLOSED]: true,
      [MRState.AWARDING_AUCTION]: true,
      [MRState.AUCTION_CLOSED]: false,
    }
    return isRestartEnabledMap[this.mrState]
  }

  get isSetPriceEnabled (): boolean {
    const IsSetPriceEnabledMap = {
      [MRState.ROUND_INIT]: true,
      [MRState.ROUND_PRICE_SET]: true,
      [MRState.ROUND_CLOCK_RUNNING]: false,
      [MRState.ROUND_CLOCK_PAUSED]: false,
      [MRState.ROUND_CLOSED]: false,
      [MRState.AWARDING_AUCTION]: false,
      [MRState.AUCTION_CLOSED]: false,
    }
    return IsSetPriceEnabledMap[this.mrState]
  }

  get isGoEnabled (): boolean {
    const IsGoEnabledMap = {
      [MRState.ROUND_INIT]: false,
      [MRState.ROUND_PRICE_SET]: true,
      [MRState.ROUND_CLOCK_RUNNING]: false,
      [MRState.ROUND_CLOCK_PAUSED]: true,
      [MRState.ROUND_CLOSED]: false,
      [MRState.AWARDING_AUCTION]: false,
      [MRState.AUCTION_CLOSED]: false,
    }
    return IsGoEnabledMap[this.mrState]
  }

  get isPauseEnabled (): boolean {
    const IsPauseEnabledMap = {
      [MRState.ROUND_INIT]: false,
      [MRState.ROUND_PRICE_SET]: false,
      [MRState.ROUND_CLOCK_RUNNING]: true,
      [MRState.ROUND_CLOCK_PAUSED]: false,
      [MRState.ROUND_CLOSED]: false,
      [MRState.AWARDING_AUCTION]: false,
      [MRState.AUCTION_CLOSED]: false,
    }
    return IsPauseEnabledMap[this.mrState]
  }

  get isEndEnabled (): boolean {
    const IsEndEnabledMap = {
      [MRState.ROUND_INIT]: false,
      [MRState.ROUND_PRICE_SET]: false,
      [MRState.ROUND_CLOCK_RUNNING]: false,
      [MRState.ROUND_CLOCK_PAUSED]: true,
      [MRState.ROUND_CLOSED]: false,
      [MRState.AWARDING_AUCTION]: false,
      [MRState.AUCTION_CLOSED]: false,
    }
    return IsEndEnabledMap[this.mrState]
  }

  get isNextEnabled (): boolean {
    const IsNextEnabledMap = {
      [MRState.ROUND_INIT]: false,
      [MRState.ROUND_PRICE_SET]: false,
      [MRState.ROUND_CLOCK_RUNNING]: false,
      [MRState.ROUND_CLOCK_PAUSED]: false,
      [MRState.ROUND_CLOSED]: true,
      [MRState.AWARDING_AUCTION]: false,
      [MRState.AUCTION_CLOSED]: false,
    }
    return IsNextEnabledMap[this.mrState]
  }

  restart () {
    this.$emit('restart')
  }

  setPrice () {
    this.$emit('showSetPrice')
  }

  go () {
    this.$emit('go')
  }

  pause () {
    this.$emit('pause')
  }

  endAuction () {
    this.$emit('endAuction')
  }

  next () {
    this.$emit('next')
  }
}
</script>

<style lang="less">
.AuctionFlowController {
  display: flex;
  align-items: center;

  &__button {
    flex: 1 1;

    & + & {
     // font-size: 12px;
      margin-left: 2px;
    }
  }
}
</style>
