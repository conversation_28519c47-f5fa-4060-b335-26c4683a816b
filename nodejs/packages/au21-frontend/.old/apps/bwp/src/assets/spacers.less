// Could be optimized with `for each` and lists.
// Not a huge priority if at all though.

.mt-1 {
  margin-top: @au-spacing-one !important;
}
.mr-1 {
  margin-right: @au-spacing-one !important;
}
.mb-1 {
  margin-bottom: @au-spacing-one !important;
}
.ml-1 {
  margin-left: @au-spacing-one !important;
}

.mt-2 {
  margin-top: @au-spacing-two !important;
}
.mr-2 {
  margin-right: @au-spacing-two !important;
}
.mb-2 {
  margin-bottom: @au-spacing-two !important;
}
.ml-2 {
  margin-left: @au-spacing-two !important;
}

.mt-3 {
  margin-top: @au-spacing-three !important;
}
.mr-3 {
  margin-right: @au-spacing-three !important;
}
.mb-3 {
  margin-bottom: @au-spacing-three !important;
}
.ml-3 {
  margin-left: @au-spacing-three !important;
}