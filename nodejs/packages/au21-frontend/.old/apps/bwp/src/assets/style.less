@import './reset';
@import './variables';
@import './spacers';

.au-header {
  font-size: 18px;
  margin-bottom: 6px;
}

.au-page {
  background-color: @au-body-background;
  color: @au-text-color;
}

.pseudo-input {
  background-color: @au-pseudo-input-color;
  border: solid @au-background 1px;
  border-radius: 4px;
  color: black;
  cursor: text;
  display: inline-flex;
  overflow: auto;
  padding: 1px 4px;
  width: 100%;
}

.test-only {
  color: #ff1123;
}

.text-bold {
  font-weight: 700;
}

.text-small {
  font-size: 12px;
}

.text-warning {
  color: @au-text-color-warning;
}

.text-center {
  text-align: center;
}

.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.fill-container {
  width: 100%;
  height: 100%;
}


.table-cell-wrapper {
  width: 100%;
  height: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.form-block {
  background-color: @au-background;
  margin: 2px;
  padding: 4px;
  border-radius: 5px;
}

.form-block-container {
  border: #9aa6b1 2px solid;
  border-radius: 8px;
  padding: 1px;
  background-color: #666666;
  height: 100%;
}

.text-link {
  color: @au-text-color;
  text-decoration: underline;

  &:hover {
    color: #e3dddd;
  }
}

.text-link-inverted {
  color: @au-text-color-secondary;
  text-decoration: underline;

  &:hover {
    color: #444444;
  }
}

.border-left {
  border-left: 1px solid #696969;
}

.border-right {
  border-right: 1px solid #696969;
}

.border-bottom {
  border-bottom: 1px solid #696969;
}

.table--border-center {
  td + td, th + th {
    border-left: 1px solid #696969;
  }
}

.au-markup-table {
  color: rgba(0, 0, 0, 0.65);
  background-color: white;
  td {
    padding: 2px;
    &:not(:first-child) {
      //border-left: solid 1px rgb(134, 128, 128);
    }
  }
  tr {
    background-color: white;
    &:nth-child(even) {
      //background-color: rgb(239, 243, 250);
    }
  }
}

.content {
  font-family: Helvetica;
  i {
    font-style: italic;
  }
  a {
    color: inherit;
  }
  li {
    padding-left: 1rem;
    position: relative;
    list-style: disc;
    color: @au-text-color-secondary;
  }
}
