<template>
  <canvas ref="experience"></canvas>
</template>

 <script setup lang="ts">
import {ref} from "vue";
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";
import * as THREE from 'three';

const experience = ref<HTMLCanvasElement | null>(null);
const {scene, camera} = useThreeJSGPT4o(experience)

// Define the data for the chart
const data = [
  { buyVolume: 10, sellVolume: 5 },
  { buyVolume: 15, sellVolume: 8 },
  { buyVolume: 12, sellVolume: 3 },
  { buyVolume: 18, sellVolume: 10 },
];

// Create the geometry and material for the ribbons
const buyGeometry = new THREE.BufferGeometry();
const sellGeometry = new THREE.BufferGeometry();

const buyPositions = [];
const sellPositions = [];

data.forEach((trader, index) => {
  buyPositions.push(index, trader.buyVolume / 2, 0);
  buyPositions.push(index, 0, 0);
  sellPositions.push(index, -trader.sellVolume / 2, 0);
  sellPositions.push(index, 0, 0);
});

buyGeometry.setAttribute('position', new THREE.Float32BufferAttribute(buyPositions, 3));
sellGeometry.setAttribute('position', new THREE.Float32BufferAttribute(sellPositions, 3));

const buyMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00, side: THREE.DoubleSide, transparent: true, opacity: 0.6 });
const sellMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000, side: THREE.DoubleSide, transparent: true, opacity: 0.6 });

const buyRibbon = new THREE.Mesh(buyGeometry, buyMaterial);
buyRibbon.rotation.x = Math.PI / 2;
scene.add(buyRibbon);

const sellRibbon = new THREE.Mesh(sellGeometry, sellMaterial);
sellRibbon.rotation.x = Math.PI / 2;
scene.add(sellRibbon);

camera.position.z = 30


</script>

