<script setup lang="ts">
import {BoxGeometry, Mesh, MeshBasicMaterial, PerspectiveCamera, Scene, SphereGeometry, WebGLRenderer} from 'three'
import {computed, onMounted, ref, watch} from "vue";
import {useWindowSize} from "@vueuse/core";
import {OrbitControls} from "three/examples/jsm/controls/OrbitControls.js"
import {useTweakPaneDM} from "../../composables/useThreeJSGPT4o.ts.txt";

const experience = ref<HTMLCanvasElement | null>(null);

let renderer: WebGLRenderer;
let camera: PerspectiveCamera;
let controls: OrbitControls;

const scene = new Scene();

camera = new PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
camera.position.z = 5;
scene.add(camera);

const {width, height} = useWindowSize();
const aspectRatio = computed(() => width.value / height.value);

function updateRenderer() {
  if(renderer) {
    renderer.setSize(width.value, height.value);
    renderer.setPixelRatio(window.devicePixelRatio);
  }
}

function updateCamera() {
  if(camera) {
    camera.aspect = aspectRatio.value;
    camera.updateProjectionMatrix();
  }
}

watch(aspectRatio, () => {
  updateCamera()
  updateRenderer()
});

const cube = new Mesh(
    new BoxGeometry(1, 1, 1, 32, 32),
    new MeshBasicMaterial({color: 0x008080})
);
scene.add(cube);
const {fpsGraph, pane} = useTweakPaneDM()
const loop= ()=> {
  if(renderer && camera) {
    fpsGraph.begin();
    renderer.render(scene, camera);
    controls.update()
    fpsGraph.end()
    requestAnimationFrame(loop);
  }
}

onMounted(() => {
  renderer = new WebGLRenderer({
    canvas: experience.value as HTMLCanvasElement,
    antialias: true
  });
  controls = new OrbitControls(camera, renderer.domElement)
  controls.enableDamping = true

  updateCamera()
  updateRenderer()
  loop();
})
</script>

<template>
  <canvas ref="experience"></canvas>
</template>
