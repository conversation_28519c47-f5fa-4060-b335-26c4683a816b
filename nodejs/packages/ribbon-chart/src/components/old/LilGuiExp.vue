<template>
  <div>
    <div>
      {{title}}
    </div>
  <div class="app">
    <DatGui
        closeText="Close controls"
        openText="Open controls"
        closePosition="bottom"
    >
      <DatColor v-model="background" label="Background" />
      <DatNumber v-model="titleFontSize" label="Title font-size" />
      <DatString v-model="title" label="Title" />
      <DatButton @click="triggerAlert" label="Trigger alert" />
      <DatFolder label="Picture">
        <DatSelect v-model="pictureUrl" :items="pictures" label="Picture" />
        <DatBoolean v-model="showPicture" label="Show Picture" />
        <DatFolder label="Box shadow">
          <DatBoolean
              v-model="boxShadow.disabled"
              :label="boxShadow.disabled ? 'Enable options' : 'Disable options' "/>
          <DatNumber
              v-model="boxShadow.offsetX"
              :min="-100"
              :max="100"
              :step="1"
              label="Offset X"
              :disabled="boxShadow.disabled"
          />
          <DatNumber
              v-model="boxShadow.offsetY"
              :min="-100"
              :max="100"
              :step="1"
              label="Offset Y"
              :disabled="boxShadow.disabled"
          />
          <DatNumber
              v-model="boxShadow.blurRadius"
              :min="0"
              :max="100"
              :step="1"
              label="Blur radius"
              :disabled="boxShadow.disabled"
          />
          <DatNumber v-model="boxShadow.spreadRadius" label="Spread radius" :disabled="boxShadow.disabled" />
          <DatColor v-model="boxShadow.color" label="Color" :disabled="boxShadow.disabled" />
        </DatFolder>
      </DatFolder>
    </DatGui>
  </div>
  </div>
</template>

<script setup lang="ts">
import {DatGui, DatBoolean, DatButton, DatColor, DatFolder, DatString,
  DatNumber, DatSelect} from
      '@cyrilf/vue-dat-gui';
import { computed, ref } from "vue";

const pictures = [
  {
    name: "forest",
    value:
        "https://images.unsplash.com/photo-1516214104703-d870798883c5?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=750&q=80",
  },
  {
    name: "mountain",
    value:
        "https://images.unsplash.com/photo-1526080676457-4544bf0ebba9?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=750&q=80",
  },
  {
    name: "beach",
    value:
        "https://images.unsplash.com/photo-1520942702018-0862200e6873?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=750&q=80",
  },
];

const background = ref("#cdeecc");
const titleColor = ref("#077d43");
const titleFontSize = ref(75);
const title = ref("vue-dat-gui");
const showPicture = ref(true);
const boxShadow = ref({
  disabled: false,
  offsetX: 27,
  offsetY: 27,
  blurRadius: 75,
  spreadRadius: 2,
  color: "rgba(3, 23, 6, 1)",
});

const pictureStyle = computed(() => {
  const { offsetX, offsetY, blurRadius, spreadRadius, color } = boxShadow.value;
  return {
    "box-shadow": `${offsetX}px ${offsetY}px ${blurRadius}px ${spreadRadius}px ${color}`,
  };
});

const triggerAlert = () => alert("Yeah, you pressed it!");

let pictureUrl = ref(pictures[0].value);
const nextPicture = () => {
  const currentIndex = pictures.findIndex(
      (picture) => picture.value === pictureUrl.value
  );
  const nextIndex = (currentIndex + 1) % pictures.length;
  pictureUrl.value = pictures[nextIndex].value;
};
</script>

<style scoped>

</style>
