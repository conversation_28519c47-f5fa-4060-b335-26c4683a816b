<template>
  <canvas ref="experience"></canvas>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";
import {BoxGeometry, Mesh, MeshBasicMaterial, SphereGeometry} from "three";

const experience = ref<HTMLCanvasElement | null>(null);
const {scene, camera} = useThreeJSGPT4o(experience)

const cube = new Mesh(
    new BoxGeometry(1, 1, 1, 32, 32),
    new MeshBasicMaterial({color: 0x008080})
);
scene.add(cube);

const sphere = new Mesh(
    new SphereGeometry(1, 20, 20),
    new MeshBasicMaterial({color: 0x080808})
);

//scene.add(sphere);
camera.position.z = 5

</script>

