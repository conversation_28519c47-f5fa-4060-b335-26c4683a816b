<script setup lang="ts">
import { TresCanvas } from "@tresjs/core"
import { OrbitControls, Sphere } from "@tresjs/cientos"
import {ref} from 'vue'

import { shallowRef, watch } from 'vue'

const sphereRef = shallowRef()

watch(sphereRef, (value) => {
	console.log(value) // Really for a log?!!! 😫
})

const x = ref(3)

</script>

<template>
	<TresCanvas>
		<TresPerspectiveCamera :position="[0, 2, 5]" />
		<Sphere
			ref="sphereRef"
			:scale="0.5"
		/>
		<OrbitControls />
	</TresCanvas>
</template>

