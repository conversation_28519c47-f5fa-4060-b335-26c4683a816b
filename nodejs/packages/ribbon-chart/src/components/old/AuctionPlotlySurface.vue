<template>
  <div ref="chartContainer"></div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Plotly from 'plotly.js-dist'
import {Auction} from "../../stores/auction-model";

const props = defineProps<{
  mockAuction: Auction;
}>();

const chartContainer = ref<HTMLDivElement | null>(null);

onMounted(() => {
  renderChart();
});

function renderChart() {
  if (!chartContainer.value) return;

  const rounds = props.mockAuction.rounds.map(round => `Round ${round.roundNumber}`);
  const traderIds = props.mockAuction.rounds[0].traderOrders.map(trader => trader.traderId);

  const buyVolumes = traderIds.map(traderId => {
    return {
      x: rounds,
      y: props.mockAuction.rounds.map(round => {
        const trader = round.traderOrders.find(t => t.traderId === traderId);
        return trader ? trader.buyVolume : 0;
      }),
      name: `Trader ${traderId} (Buy)`,
      type: 'scatter3d',
      mode: 'lines',
      line: {
        width: 10,
        color: `rgb(${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)})`,
      },
    };
  });

  const sellVolumes = traderIds.map(traderId => {
    return {
      x: rounds,
      y: props.mockAuction.rounds.map(round => {
        const trader = round.traderOrders.find(t => t.traderId === traderId);
        return trader ? trader.sellVolume : 0;
      }),
      name: `Trader ${traderId} (Sell)`,
      type: 'scatter3d',
      mode: 'lines',
      line: {
        width: 10,
        color: `rgb(${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)})`,
      },
    };
  });

  const data = [...buyVolumes, ...sellVolumes];

  const layout = {
    title: 'Mock Auction Results (3D Ribbon Chart)',
    scene: {
      xaxis: { title: 'Round' },
      yaxis: { title: 'Volume' },
      zaxis: { title: 'Trader' },
    },
    margin: {
      l: 0,
      r: 0,
      b: 0,
      t: 50,
    },
  };

  Plotly.newPlot(chartContainer.value, data, layout);
}
</script>
