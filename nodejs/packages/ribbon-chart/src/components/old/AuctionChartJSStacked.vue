<template>
  <div>
    <canvas ref="chartCanvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import Chart from 'chart.js/auto';
import {Auction} from "../../stores/auction-model";

// interface Trader {
//   id: string;
//   buyVolume: number;
//   sellVolume: number;
// }
//
// interface Round {
//   roundNumber: number;
//   traders: Trader[];
// }
//
// interface Auction {
//   rounds: Round[];
// }

const props = defineProps<{
  mockAuction: Auction;
}>();

const chartCanvas = ref<HTMLCanvasElement | null>(null);

onMounted(() => {
  renderChart();
});

function renderChart() {
  if (!chartCanvas.value) return;

  const ctx = chartCanvas.value.getContext('2d');
  if (!ctx) return;

  const rounds = props.mockAuction.rounds.map(round => `Round ${round.roundNumber}`);
  const traderIds = props.mockAuction.rounds[0].traderOrders.map(trader => trader.traderId);

  const buyVolumes = traderIds.map(traderId => {
    return {
      label: `Trader ${traderId} (Buy)`,
      data: props.mockAuction.rounds.map(round => {
        const trader = round.traderOrders.find(t => t.traderId === traderId);
        return trader ? trader.buyVolume : 0;
      }),
      backgroundColor: `rgba(${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, 0.7)`,
      stack: 'buy',
    };
  });

  const sellVolumes = traderIds.map(traderId => {
    return {
      label: `Trader ${traderId} (Sell)`,
      data: props.mockAuction.rounds.map(round => {
        const trader = round.traderOrders.find(t => t.traderId === traderId);
        return trader ? trader.sellVolume : 0;
      }),
      backgroundColor: `rgba(${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, ${Math.floor(Math.random() * 256)}, 0.7)`,
      stack: 'sell',
    };
  });

  new Chart(ctx, {
    type: 'bar',
    data: {
      labels: rounds,
      datasets: [...buyVolumes, ...sellVolumes],
    },
    options: {
      responsive: true,
      plugins: {
        title: {
          display: true,
          text: 'Mock Auction Results by Trader',
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        },
      },
      scales: {
        x: {
          stacked: true,
        },
        y: {
          stacked: true,
          title: {
            display: true,
            text: 'Volume',
          },
        },
      },
    },
  });
}
</script>
