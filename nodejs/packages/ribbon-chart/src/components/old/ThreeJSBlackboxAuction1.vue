<template>
  <canvas ref="experience"></canvas>
</template>

 <script setup lang="ts">
import {ref} from "vue";
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";
import * as THREE from 'three';

const experience = ref<HTMLCanvasElement | null>(null);
const {scene, camera} = useThreeJSGPT4o(experience)

// Define the data for the chart
const data = [
  { buyVolume: 10, sellVolume: 5 },
  { buyVolume: 15, sellVolume: 8 },
  { buyVolume: 12, sellVolume: 3 },
  { buyVolume: 18, sellVolume: 10 },
];


// Create the geometry and material for the ribbons
const ribbonGeometry = new THREE.PlaneGeometry(1, 1);
const buyMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00, side: THREE.DoubleSide, transparent: true, opacity: 0.6 });
const sellMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000, side: THREE.DoubleSide, transparent: true, opacity: 0.6 });

// Iterate through the data and create a ribbon for each trader's buy and sell volume
data.forEach((trader, index) => {
  const buyRibbon = new THREE.Mesh(ribbonGeometry, buyMaterial);
  buyRibbon.position.set(index, trader.buyVolume / 2, -1);
  buyRibbon.rotation.x = Math.PI / 2;
  scene.add(buyRibbon);

  const sellRibbon = new THREE.Mesh(ribbonGeometry, sellMaterial);
  sellRibbon.position.set(index, -trader.sellVolume / 2, 0);
  sellRibbon.rotation.x = Math.PI / 2;
  scene.add(sellRibbon);
});

camera.position.z = 30

</script>

