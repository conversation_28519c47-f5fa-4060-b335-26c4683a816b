<template>
  <DatFolder label="Helpers" :open>
    <DatButton label="Toggle AxesHelper" @click="toggleHelper(axisHelperName, axisHelper)" />
    <DatButton label="Toggle GridHelper" @click="toggleGridHelper" />
    <DatNumber label="Grid Size" v-model="gridSize" :min="1" :max="200" :step="1" />
    <DatButton label="Toggle PolarGridHelper" @click="toggleHelper(polarGridHelperName, polarGridHelper)" />
  </DatFolder>
</template>

<script setup lang="ts">
import {ref, watch, inject, onMounted, defineProps} from 'vue';
import * as THREE from 'three';
import { DatFolder, DatButton, DatNumber } from '@cyrilf/vue-dat-gui';

defineProps<{
  open: boolean
}>()

const scene = inject('scene') as THREE.Scene;

const axisHelperName = 'axesHelper';
const gridHelperName = 'gridHelper';
const polarGridHelperName = 'polarGridHelper';

const gridSize = ref(30);

const axisHelper = () => {
  const axesHelper = new THREE.AxesHelper(5);
  axesHelper.name = axisHelperName;
  scene.add(axesHelper);
};

const gridHelper = (size: number) => {
  const divisions = 10;
  const colorCenterLine = 0x00ff00;
  const colorGrid = 0xffffff;
  const helper = new THREE.GridHelper(size, divisions, colorCenterLine, colorGrid);
  helper.name = gridHelperName;
  scene.add(helper);
};

const polarGridHelper = () => {
  const radius = 10;
  const radials = 16;
  const circles = 8;
  const divisions = 64;
  const helper = new THREE.PolarGridHelper(radius, radials, circles, divisions);
  helper.name = polarGridHelperName;
  scene.add(helper);
};

const toggleHelper = (name: string, addFn: () => void) => {
  const currentObject = scene.getObjectByName(name);
  if (currentObject) {
    scene.remove(currentObject);
  } else {
    addFn();
  }
};

const toggleGridHelper = () => {
  const currentGridHelper = scene.getObjectByName(gridHelperName);
  if (currentGridHelper) {
    scene.remove(currentGridHelper);
  } else {
    gridHelper(gridSize.value);
  }
};

watch(gridSize, (newSize) => {
  const currentGridHelper = scene.getObjectByName(gridHelperName);
  if (currentGridHelper) {
    scene.remove(currentGridHelper);
    gridHelper(newSize);
  }
});

onMounted(()=>{

  // if (open){
  //   gridHelper(gridSize.value);
  //
  // }
})
</script>
