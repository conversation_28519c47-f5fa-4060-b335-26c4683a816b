<template>
  <div>
    <canvas ref="canvas"></canvas>
    <DatGui :is-open="isOpen">
      <ControlPerspectiveCamera
          v-if="isPerspective"
          :camera="camera"
          :orbit-controls="orbitControls"
      />
<!--      <ControlOrthoCamera-->
<!--          v-if="!isPerspective"-->
<!--          :camera="camera"-->
<!--          :orbit-controls="orbitControls"-->
<!--      />-->
    </DatGui>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { DatGui } from '@cyrilf/vue-dat-gui'
import ControlPerspectiveCamera from './PerspectiveCameraControls.vue'
import ControlOrthoCamera from './OrthoCameraControls.vue'
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";

const canvas = ref<HTMLCanvasElement | null>(null)

const { camera, scene, orbitControls, renderer, axesHelper } = useThreeJSGPT4o(canvas)

const isOpen = ref(true)
const isPerspective = ref(true) // Toggle this to switch between camera types

onMounted(() => {
  if (canvas.value) {
    renderer.setSize(window.innerWidth, window.innerHeight)
    canvas.value.appendChild(renderer.domElement)
  }
})
</script>

<style scoped>
canvas {
  width: 100%;
  height: 100%;
}
</style>
