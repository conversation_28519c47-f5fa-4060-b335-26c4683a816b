<template>
  <DatFolder label="Tube Geometry">
    <DatNumber label="Tubular Segments" v-model="tubeProps.tubularSegments" :min="1" :max="200" :step="1" />
    <DatNumber label="Radius" v-model="tubeProps.radius" :min="0.1" :max="10" :step="0.1" />
    <DatNumber label="Radial Segments" v-model="tubeProps.radialSegments" :min="1" :max="200" :step="1" />
    <DatBoolean label="Closed" v-model="tubeProps.closed" />
    <DatNumber label="Scale X" v-model="tubeProps.scaleX" :min="0.1" :max="10" :step="0.1" />
    <DatNumber label="Scale Y" v-model="tubeProps.scaleY" :min="0.1" :max="10" :step="0.1" />
  </DatFolder>
</template>

<script setup lang="ts">
import { reactive, watch, onMounted } from 'vue';
import * as THREE from 'three';
import { DatFolder, Dat<PERSON><PERSON><PERSON>, DatBoolean } from '@cyrilf/vue-dat-gui';

const props = defineProps<{
  paths: THREE.Curve<THREE.Vector3>[];
}>();

const emit = defineEmits<{
  (event: 'meshesCreated', meshes: THREE.Mesh[]): void;
}>();

const tubeProps = reactive({
  tubularSegments: 100,
  radius: 2,
  radialSegments: 100,
  closed: false,
  scaleX: 1,
  scaleY: 1,
});

const meshes: THREE.Mesh[] = [];

function createTubeMesh(path: THREE.Curve<THREE.Vector3>) {
  const geometry = new THREE.TubeGeometry(
      path,
      tubeProps.tubularSegments,
      tubeProps.radius,
      tubeProps.radialSegments,
      tubeProps.closed
  );
  const material = new THREE.MeshBasicMaterial({ color: 0xffff00 });
  const mesh = new THREE.Mesh(geometry, material);
  meshes.push(mesh);
  return mesh;
}

function updateGeometry() {
  meshes.forEach((mesh) => {
    const newGeometry = new THREE.TubeGeometry(
        mesh.geometry.parameters.path,
        tubeProps.tubularSegments,
        tubeProps.radius,
        tubeProps.radialSegments,
        tubeProps.closed
    );
    mesh.geometry.dispose();
    mesh.geometry = newGeometry;
  });
}

function updateScale() {
  meshes.forEach((mesh) => {
    mesh.scale.set(tubeProps.scaleX, tubeProps.scaleY, 1);
  });
}

watch(
    () => [tubeProps.tubularSegments, tubeProps.radius, tubeProps.radialSegments, tubeProps.closed],
    () => {
      updateGeometry();
    }
);

watch(
    () => [tubeProps.scaleX, tubeProps.scaleY],
    () => {
      updateScale();
    }
);

onMounted(() => {
  // Create tube meshes for each path
  props.paths.forEach((path) => {
    createTubeMesh(path);
  });

  // Emit the created meshes to the parent component
  emit('meshesCreated', meshes);
});
</script>
