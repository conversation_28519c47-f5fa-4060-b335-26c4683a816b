<template>
  <DatFolder label="Ambient Light" :open>
    <DatNumber label="Intensity" v-model="ambientLightProps.intensity" :min="0" :max="5" :step="0.1" />
    <DatColor label="Color" v-model="ambientLightProps.color" />
  </DatFolder>
</template>

<script setup lang="ts">
import { reactive, watch } from 'vue';
import * as THREE from 'three';
import { DatFolder, DatNumber, DatColor } from '@cyrilf/vue-dat-gui';

const props = defineProps<{
  light: THREE.AmbientLight;
  open:boolean;
}>();

const colorHolder = new THREE.Color(props.light.color);

const ambientLightProps = reactive({
  color: colorHolder.getStyle(),
  intensity: props.light.intensity,
});

watch(
    () => ambientLightProps.intensity,
    (newIntensity) => {
      props.light.intensity = newIntensity;
    }
);

watch(
    () => ambientLightProps.color,
    (newColor) => {
      props.light.color.setStyle(newColor);
    }
);
</script>
