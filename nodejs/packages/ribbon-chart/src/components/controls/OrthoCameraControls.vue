<template>
  <DatFolder label="Orthographic Camera">
    <DatNumber label="left" v-model="orthoProps.left" :min="-400" :max="-10" :step="1" />
    <DatNumber label="right" v-model="orthoProps.right" :min="10" :max="400" :step="1" />
    <DatNumber label="top" v-model="orthoProps.top" :min="0" :max="200" :step="1" />
    <DatNumber label="bottom" v-model="orthoProps.bottom" :min="-200" :max="0" :step="1" />
    <DatNumber label="near" v-model="orthoProps.near" :min="0.1" :max="100" :step="1" />
    <DatNumber label="far" v-model="orthoProps.far" :min="0.1" :max="2000" :step="1" />
    <DatNumber label="zoom" v-model="orthoProps.zoom" :min="0.1" :max="100" :step="1" />
    <DatNumber label="lookAtX" v-model="lookAtProps.lookAtX" :min="-10" :max="10" :step="0.1" />
    <DatNumber label="lookAtY" v-model="lookAtProps.lookAtY" :min="-10" :max="10" :step="0.1" />
    <DatNumber label="lookAtZ" v-model="lookAtProps.lookAtZ" :min="-10" :max="10" :step="0.1" />
  </DatFolder>
</template>

<script setup lang="ts">
import { reactive, watch, defineProps } from 'vue'
import { DatFolder, DatNumber } from '@cyrilf/vue-dat-gui'
import * as THREE from 'three'

const props = defineProps<{
  camera: THREE.OrthographicCamera
  orbitControls: any
}>()

const lookAtProps = reactive({
  lookAtX: 0,
  lookAtY: 0,
  lookAtZ: 0
})

const orthoProps = reactive({
  left: props.camera.left,
  right: props.camera.right,
  top: props.camera.top,
  bottom: props.camera.bottom,
  near: props.camera.near,
  far: props.camera.far,
  zoom: props.camera.zoom
})

watch(orthoProps, () => {
  props.camera.left = orthoProps.left
  props.camera.right = orthoProps.right
  props.camera.top = orthoProps.top
  props.camera.bottom = orthoProps.bottom
  props.camera.near = orthoProps.near
  props.camera.far = orthoProps.far
  props.camera.zoom = orthoProps.zoom
  props.camera.updateProjectionMatrix()
  props.camera.lookAt(new THREE.Vector3(lookAtProps.lookAtX, lookAtProps.lookAtY, lookAtProps.lookAtZ))
  props.orbitControls.target.set(lookAtProps.lookAtX, lookAtProps.lookAtY, lookAtProps.lookAtZ)
  props.orbitControls.update()
}, { deep: true })
</script>
