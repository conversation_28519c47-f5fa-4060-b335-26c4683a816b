<template>
  <DatFolder label="Perspective Camera" :open>
    <DatNumber label="fov" v-model="perspectiveProps.fov" :min="0" :max="180" :step="1" />
    <DatNumber label="aspect" v-model="perspectiveProps.aspect" :min="0.1" :max="10" :step="0.1" />
    <DatNumber label="near" v-model="perspectiveProps.near" :min="0.1" :max="100" :step="0.1" />
    <DatNumber label="far" v-model="perspectiveProps.far" :min="0.1" :max="2000" :step="0.1" />
    <DatNumber label="zoom" v-model="perspectiveProps.zoom" :min="0.1" :max="10" :step="0.1" />
    <DatNumber label="lookAtX" v-model="lookAtProps.lookAtX" :min="-10" :max="10" :step="0.1" />
    <DatNumber label="lookAtY" v-model="lookAtProps.lookAtY" :min="-10" :max="10" :step="0.1" />
    <DatNumber label="lookAtZ" v-model="lookAtProps.lookAtZ" :min="-10" :max="10" :step="0.1" />
  </DatFolder>
</template>

<script setup lang="ts">
import {reactive, watch, defineProps, inject} from 'vue'
import { DatFolder, DatNumber } from '@cyrilf/vue-dat-gui'
import * as THREE from 'three'
import {OrbitControls} from "three/examples/jsm/controls/OrbitControls.js";

defineProps<{
  open: boolean
}>()

const scene = inject('scene') as THREE.Scene;
const camera = inject('camera') as THREE.PerspectiveCamera;
const orbitControls = inject('orbitControls') as () => OrbitControls;

const lookAtProps = reactive({
  lookAtX: 0,
  lookAtY: 0,
  lookAtZ: 0
})

const perspectiveProps = reactive({
  fov: camera.fov,
  aspect: camera.aspect,
  near: camera.near,
  far: camera.far,
  zoom: camera.zoom
})

watch(perspectiveProps, () => {
  camera.fov = perspectiveProps.fov
  camera.aspect = perspectiveProps.aspect
  camera.near = perspectiveProps.near
  camera.far = perspectiveProps.far
  camera.zoom = perspectiveProps.zoom
  camera.updateProjectionMatrix()

  if (orbitControls) {
    orbitControls.target.set(lookAtProps.lookAtX, lookAtProps.lookAtY, lookAtProps.lookAtZ)
    orbitControls.update()
  }
}, { deep: true })
</script>
