<template>
  <div>
    <canvas ref="canvas"></canvas>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import * as THREE from 'three';

const canvas = ref<HTMLCanvasElement | null>(null);

onMounted(() => {
  if (canvas.value) {
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ canvas: canvas.value });

    camera.position.z = 5;
    camera.position.y = 5

    const size = 10;
    const divisions = 10;
    const colorCenterLine = 0x00ff00; // Green
    const colorGrid = 0xffffff; // White
    const gridHelper = new THREE.GridHelper(size, divisions, colorCenterLine, colorGrid);
    scene.add(gridHelper);

    function animate() {
      requestAnimationFrame(animate);
      renderer.render(scene, camera);
    }

    animate();
  }
});
</script>
