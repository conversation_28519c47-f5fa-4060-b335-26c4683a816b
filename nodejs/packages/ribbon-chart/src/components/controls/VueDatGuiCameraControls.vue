<template>
  <div>
    <canvas ref="canvas"></canvas>
    <div>
      <DatGui :is-open="isOpen">
        <DatFolder title="Perspective Camera" v-if="isPerspective">
          <DatNumber label="fov" v-model="perspectiveProps.fov" :min="0"
                     :max="180" :step="1"/>
          <DatNumber label="aspect" v-model="perspectiveProps.aspect" :min="0"
                     :max="10" :step="0.1"/>
          <DatNumber label="near" v-model="perspectiveProps.near" :min="0"
                     :max="20" :step="0.1"/>
          <DatNumber label="far" v-model="perspectiveProps.far" :min="5"
                     :max="100" :step="0.1"/>
          <DatNumber label="zoom" v-model="perspectiveProps.zoom" :min="-1"
                     :max="10" :step="0.1"/>
          <DatNumber label="lookAtX" v-model="lookAtProps.lookAtX" :min="-10"
                     :max="10" :step="0.1"/>
          <DatNumber label="lookAtY" v-model="lookAtProps.lookAtY" :min="-10"
                     :max="10" :step="0.1"/>
          <DatNumber label="lookAtZ" v-model="lookAtProps.lookAtZ" :min="-10"
                     :max="10" :step="0.1"/>
        </DatFolder>
        <DatFolder title="Orthographic Camera" v-if="!isPerspective">
          <DatNumber label="left" v-model="orthoProps.left" :min="-400"
                     :max="-10" :step="1"/>
          <DatNumber label="right" v-model="orthoProps.right" :min="10"
                     :max="400" :step="1"/>
          <DatNumber label="top" v-model="orthoProps.top" :min="0" :max="200"
                     :step="1"/>
          <DatNumber label="bottom" v-model="orthoProps.bottom" :min="-200"
                     :max="0" :step="1"/>
          <DatNumber label="near" v-model="orthoProps.near" :min="-20" :max="10"
                     :step="1"/>
          <DatNumber label="far" v-model="orthoProps.far" :min="1" :max="100"
                     :step="1"/>
          <DatNumber label="zoom" v-model="orthoProps.zoom" :min="1" :max="100"
                     :step="1"/>
          <DatNumber label="lookAtX" v-model="lookAtProps.lookAtX" :min="-10"
                     :max="10" :step="0.1"/>
          <DatNumber label="lookAtY" v-model="lookAtProps.lookAtY" :min="-10"
                     :max="10" :step="0.1"/>
          <DatNumber label="lookAtZ" v-model="lookAtProps.lookAtZ" :min="-10"
                     :max="10" :step="0.1"/>
        </DatFolder>
      </DatGui>
    </div>
  </div>
</template>

<script setup lang="ts">
import {ref, reactive, watch} from 'vue'
import * as THREE from 'three'
import {DatGui, DatFolder, DatNumber} from '@cyrilf/vue-dat-gui'
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls.js';
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";

const canvas = ref<HTMLCanvasElement | null>(null);

const {camera, scene, orbitControls} = useThreeJSGPT4o(canvas)

const perspectiveName = 'Perspective Camera'
const orthoName = 'Orthographic Camera'

const lookAtProps = reactive({
  lookAtX: 0,
  lookAtY: 0,
  lookAtZ: 0
})

const perspectiveProps = reactive({
  fov: 75,
  aspect: 2,
  near: 0.1,
  far: 1000,
  zoom: 1
})

const orthoProps = reactive({
  left: -100,
  right: 100,
  top: 100,
  bottom: -100,
  near: 0.1,
  far: 1000,
  zoom: 1
})

const isPerspective = ref(true)
const isOpen = ref(true)

const orthoCamera = new THREE.OrthographicCamera(-100, 100, 100, -100, 0.1, 1000)

// Watchers to update the camera properties
watch(perspectiveProps, () => {
  if (isPerspective.value) {
    camera.fov = perspectiveProps.fov
    camera.aspect = perspectiveProps.aspect
    camera.near = perspectiveProps.near
    camera.far = perspectiveProps.far
    camera.zoom = perspectiveProps.zoom
    camera.updateProjectionMatrix()
    //  orbitControls.target.set(lookAtProps.lookAtX, lookAtProps.lookAtY,
    //      lookAtProps.lookAtZ)
    //  orbitControls.update()
  }
}, {deep: true})

watch(orthoProps, () => {
  if (!isPerspective.value) {
    orthoCamera.left = orthoProps.left
    orthoCamera.right = orthoProps.right
    orthoCamera.top = orthoProps.top
    orthoCamera.bottom = orthoProps.bottom
    orthoCamera.near = orthoProps.near
    orthoCamera.far = orthoProps.far
    orthoCamera.zoom = orthoProps.zoom
    orthoCamera.updateProjectionMatrix()
    orthoCamera.lookAt(new THREE.Vector3(lookAtProps.lookAtX, lookAtProps.lookAtY, lookAtProps.lookAtZ))
    // orbitControls?.target.set(lookAtProps.lookAtX, lookAtProps.lookAtY,
    //     lookAtProps.lookAtZ)
    // orbitControls?.update()
  }
}, {deep: true})
</script>

<style scoped>
/* Add any additional styles if necessary */
</style>
