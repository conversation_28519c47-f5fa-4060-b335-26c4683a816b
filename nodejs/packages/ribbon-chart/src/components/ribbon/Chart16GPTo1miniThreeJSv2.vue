<template>
	<div ref="threeJsContainer" class="threejs-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";

// Data Structures
type RoundData = {
	round_number: number;
	round_data: Array<{ volume: number; trader: string }>;
};

type TraderData = {
	trader: string;
	volumes: number[]; // Volume per round, including round 0
};

// Initial Data with Round 0, Round 1, and Round 2
const data = ref<RoundData[]>([
	{
		round_number: 0,
		round_data: [
			{ volume: 0, trader: "t1" },
			{ volume: 0, trader: "t2" },
			{ volume: 0, trader: "t3" },
			{ volume: 0, trader: "t4" },
			{ volume: 0, trader: "t5" },
			{ volume: 0, trader: "t6" },
			{ volume: 0, trader: "t7" },
			{ volume: 0, trader: "t8" },
			{ volume: 0, trader: "t9" },
			{ volume: 0, trader: "t10" },
		],
	},
	{
		round_number: 1,
		round_data: [
			{ volume: 50, trader: "t1" },
			{ volume: 50, trader: "t2" },
			{ volume: 50, trader: "t3" },
			{ volume: 50, trader: "t4" },
			{ volume: 50, trader: "t5" },
			{ volume: 50, trader: "t6" },
			{ volume: 50, trader: "t7" },
			{ volume: 50, trader: "t8" },
			{ volume: 50, trader: "t9" },
			{ volume: 50, trader: "t10" },
		],
	},
	{
		round_number: 2,
		round_data: [
			{ volume: 40, trader: "t1" },
			{ volume: 35, trader: "t2" },
			{ volume: 42, trader: "t3" },
			{ volume: 41, trader: "t4" },
			{ volume: 20, trader: "t5" },
			{ volume: 25, trader: "t6" },
			{ volume: 35, trader: "t7" },
			{ volume: 33, trader: "t8" },
			{ volume: 34, trader: "t9" },
			{ volume: 12, trader: "t10" },
		],
	},
]);

// Initialize Traders Data
const traders = ref<TraderData[]>([
	{ trader: "t1", volumes: [] },
	{ trader: "t2", volumes: [] },
	{ trader: "t3", volumes: [] },
	{ trader: "t4", volumes: [] },
	{ trader: "t5", volumes: [] },
	{ trader: "t6", volumes: [] },
	{ trader: "t7", volumes: [] },
	{ trader: "t8", volumes: [] },
	{ trader: "t9", volumes: [] },
	{ trader: "t10", volumes: [] },
]);

// Populate Initial Volumes
data.value.forEach((round) => {
	round.round_data.forEach((entry) => {
		const trader = traders.value.find((t) => t.trader === entry.trader);
		trader?.volumes.push(entry.volume);
	});
});

// Three.js Setup
const threeJsContainer = ref<HTMLElement | null>(null);

let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let traderGroups: Map<string, THREE.Group> = new Map();

// Adjusted Dimensions for Ribbons
const ribbonWidth = 2; // Increased depth
const ribbonThickness = 0.1; // Decreased height
const ribbonSeparation = 2; // Separation between traders

// Assign unique colors to traders
const traderColors = [
	0xff5733, // t1
	0x33ff57, // t2
	0x3357ff, // t3
	0xff33a8, // t4
	0xa833ff, // t5
	0x33fff6, // t6
	0xff8f33, // t7
	0x8fff33, // t8
	0x338fff, // t9
	0xff3333, // t10
];

// Create Trader Ribbons
const createTraderRibbons = () => {
	traders.value.forEach((trader, index) => {
		const group = new THREE.Group();
		group.position.z = index * (ribbonWidth + ribbonSeparation);
		scene.add(group);
		traderGroups.set(trader.trader, group);

		const color = traderColors[index % traderColors.length];

		// Initial Ribbon Construction starting from Round 1
		// Skip Round 0 to avoid initial vertical step
		for (let i = 1; i < trader.volumes.length; i++) {
			const currentVolume = trader.volumes[i];
			const previousVolume = trader.volumes[i - 1];
			addRibbonStep(group, i, previousVolume, currentVolume, color);
		}
	});
};

// Add Ribbon Step as Vertical and Horizontal Boxes
const addRibbonStep = (
	group: THREE.Group,
	roundIndex: number,
	previousVolume: number,
	currentVolume: number,
	color: number
) => {
	const x = roundIndex;

	// Add vertical step only if it's not the first real round (roundIndex > 1)
	if (roundIndex > 1) {
		const verticalGeometry = new THREE.BoxGeometry(
			ribbonThickness, // Width along X-axis
			Math.abs(currentVolume - previousVolume), // Height along Y-axis
			ribbonWidth // Depth along Z-axis
		);
		const verticalMaterial = new THREE.MeshPhongMaterial({ color });
		const verticalBox = new THREE.Mesh(verticalGeometry, verticalMaterial);
		verticalBox.position.set(
			x,
			previousVolume + (currentVolume - previousVolume) / 2,
			0
		);
		group.add(verticalBox);
	}

	// Add horizontal step
	const horizontalGeometry = new THREE.BoxGeometry(
		1, // Width along X-axis
		ribbonThickness, // Height along Y-axis
		ribbonWidth // Depth along Z-axis
	);
	const horizontalMaterial = new THREE.MeshPhongMaterial({ color });
	const horizontalBox = new THREE.Mesh(horizontalGeometry, horizontalMaterial);
	horizontalBox.position.set(x + 0.5, currentVolume, 0);
	group.add(horizontalBox);
};

// Update Ribbons with New Round
const updateRibbons = (newRound: RoundData) => {
	newRound.round_data.forEach((entry) => {
		const trader = traders.value.find((t) => t.trader === entry.trader);
		if (trader) {
			const previousVolume =
				trader.volumes.length > 0
					? trader.volumes[trader.volumes.length - 1]
					: 0;
			trader.volumes.push(entry.volume);
			const group = traderGroups.get(trader.trader);
			if (group) {
				const traderIndex = traders.value.findIndex(
					(t) => t.trader === trader.trader
				);
				const color = traderColors[traderIndex % traderColors.length];
				addRibbonStep(
					group,
					trader.volumes.length - 1,
					previousVolume,
					entry.volume,
					color
				);
			}
		}
	});
};

// Initialize Three.js
const initThreeJS = () => {
	if (!threeJsContainer.value) return;

	// Scene
	scene = new THREE.Scene();
	scene.background = new THREE.Color(0xf0f0f0);

	// Camera
	const aspect =
		threeJsContainer.value.clientWidth / threeJsContainer.value.clientHeight;
	camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
	camera.position.set(30, 50, 60);
	camera.lookAt(0, 0, 0);

	// Renderer
	renderer = new THREE.WebGLRenderer({ antialias: true });
	renderer.setSize(
		threeJsContainer.value.clientWidth,
		threeJsContainer.value.clientHeight
	);
	threeJsContainer.value.appendChild(renderer.domElement);

	// Controls
	controls = new OrbitControls(camera, renderer.domElement);
	controls.enableDamping = true;
	controls.dampingFactor = 0.05;
	controls.minDistance = 10;
	controls.maxDistance = 200;
	controls.update();

	// Lights
	const ambientLight = new THREE.AmbientLight(0x404040, 2);
	scene.add(ambientLight);

	const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
	directionalLight.position.set(50, 50, 50);
	scene.add(directionalLight);

	// Helpers
	const axesHelper = new THREE.AxesHelper(50);
	scene.add(axesHelper);

	// Create Ribbons
	createTraderRibbons();

	animate();
};

// Animation Loop
const animate = () => {
	requestAnimationFrame(animate);
	controls.update();
	renderer.render(scene, camera);
};

// Handle Window Resize
const onWindowResize = () => {
	if (!threeJsContainer.value) return;
	const width = threeJsContainer.value.clientWidth;
	const height = threeJsContainer.value.clientHeight;
	camera.aspect = width / height;
	camera.updateProjectionMatrix();
	renderer.setSize(width, height);
};

// Simulate Adding Rounds Dynamically
let currentRound = data.value.length - 1; // Starting after round 0
const addRound = () => {
	currentRound += 1;
	const newRound: RoundData = {
		round_number: currentRound,
		round_data: traders.value.map((trader) => ({
			volume: Math.max(
				0,
				trader.volumes[trader.volumes.length - 1] -
				Math.floor(Math.random() * 10)
			),
			trader: trader.trader,
		})),
	};
	data.value.push(newRound);
	updateRibbons(newRound);
};

// Optionally, add rounds at intervals
setInterval(addRound, 2000); // Adds a new round every 2 seconds

// Uncomment the following line to add a provided round on initialization
// addProvidedRound();

onMounted(() => {
	initThreeJS();
	window.addEventListener("resize", onWindowResize);
});

onUnmounted(() => {
	window.removeEventListener("resize", onWindowResize);
	if (renderer) {
		renderer.dispose();
	}
});
</script>

<style scoped>
.threejs-container {
	width: 100%;
	height: 100vh;
	display: block;
}
</style>
