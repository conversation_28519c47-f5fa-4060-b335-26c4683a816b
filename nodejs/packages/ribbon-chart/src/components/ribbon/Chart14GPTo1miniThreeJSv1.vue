<template>
	<div ref="threeJsContainer" class="threejs-container"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue"
import * as THREE from "three"
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js"

const threeJsContainer = ref<HTMLElement | null>(null)

let scene: THREE.Scene
let camera: THREE.PerspectiveCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let group: THREE.Group

const createStaircaseRibbon = () => {
	const numSteps = 10
	const stepWidth = 2
	const stepHeight = 1
	const stepDepth = 1

	group = new THREE.Group()

	for (let i = 0; i < numSteps; i++) {
		const geometry = new THREE.BoxGeometry(stepWidth, stepHeight, stepDepth)
		const material = new THREE.MeshPhongMaterial({ color: 0x0077be })
		const stepMesh = new THREE.Mesh(geometry, material)
		stepMesh.position.set(i * stepDepth, i * stepHeight, 0)
		group.add(stepMesh)
	}

	scene.add(group)
}

const initThreeJS = () => {
	if (!threeJsContainer.value) return

	scene = new THREE.Scene()
	scene.background = new THREE.Color(0xf0f0f0)

	const aspect = threeJsContainer.value.clientWidth / threeJsContainer.value.clientHeight
	camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000)
	camera.position.set(15, 15, 15)

	renderer = new THREE.WebGLRenderer({ antialias: true })
	renderer.setSize(threeJsContainer.value.clientWidth, threeJsContainer.value.clientHeight)
	threeJsContainer.value.appendChild(renderer.domElement)

	controls = new OrbitControls(camera, renderer.domElement)
	controls.enableDamping = true
	controls.dampingFactor = 0.05
	controls.minDistance = 5
	controls.maxDistance = 50
	controls.update()

	const ambientLight = new THREE.AmbientLight(0x404040, 2)
	scene.add(ambientLight)

	const directionalLight = new THREE.DirectionalLight(0xffffff, 1)
	directionalLight.position.set(10, 10, 10)
	scene.add(directionalLight)

	const axesHelper = new THREE.AxesHelper(10)
	scene.add(axesHelper)

	createStaircaseRibbon()

	animate()
}

const animate = () => {
	requestAnimationFrame(animate)
	controls.update()
	renderer.render(scene, camera)
}

const onWindowResize = () => {
	if (!threeJsContainer.value) return
	const width = threeJsContainer.value.clientWidth
	const height = threeJsContainer.value.clientHeight
	camera.aspect = width / height
	camera.updateProjectionMatrix()
	renderer.setSize(width, height)
}

onMounted(() => {
	initThreeJS()
	window.addEventListener('resize', onWindowResize)
})

onUnmounted(() => {
	window.removeEventListener('resize', onWindowResize)
	if (renderer) {
		renderer.dispose()
	}
})
</script>

<style scoped>
.threejs-container {
	width: 100%;
	height: 100vh;
	display: block;
}
</style>
