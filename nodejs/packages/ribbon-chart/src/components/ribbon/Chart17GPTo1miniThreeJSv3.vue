<template>
	<div class="container">
		<!-- UI Controls -->
		<div class="controls">
			<!-- Slope vs Vertical Toggle -->
			<div class="control-group">
				<label>
					<input type="checkbox" v-model="useSlope" />
					Connect with Slope
				</label>
			</div>

			<!-- Material Selection -->
			<div class="control-group">
				<span>Material:</span>
				<label>
					<input type="radio" value="basic" v-model="selectedMaterial" />
					Basic
				</label>
				<label>
					<input type="radio" value="standard" v-model="selectedMaterial" />
					MeshStandardMaterial
				</label>
				<label>
					<input type="radio" value="lambert" v-model="selectedMaterial" />
					MeshLambertMaterial
				</label>
			</div>

			<!-- Texture Selection -->
			<div class="control-group">
				<span>Texture:</span>
				<label>
					<input type="radio" value="none" v-model="selectedTexture" />
					None
				</label>
				<label>
					<input type="radio" value="grid" v-model="selectedTexture" />
					Grid
				</label>
				<label>
					<input type="radio" value="checker" v-model="selectedTexture" />
					Checker
				</label>
			</div>

			<!-- Ribbon Dimensions Sliders -->
			<div class="control-group">
				<span>Ribbon Width:</span>
				<input
					type="range"
					min="0.1"
					max="5"
					step="0.1"
					v-model.number="ribbonWidth"
				/>
				<span>{{ ribbonWidth.toFixed(1) }}</span>
			</div>
			<div class="control-group">
				<span>Ribbon Thickness:</span>
				<input
					type="range"
					min="0.05"
					max="2"
					step="0.05"
					v-model.number="ribbonThickness"
				/>
				<span>{{ ribbonThickness.toFixed(2) }}</span>
			</div>
			<div class="control-group">
				<span>Ribbon Separation:</span>
				<input
					type="range"
					min="1"
					max="5"
					step="0.5"
					v-model.number="ribbonSeparation"
				/>
				<span>{{ ribbonSeparation }}</span>
			</div>

			<!-- Instance Mesh Toggle -->
			<div class="control-group">
				<label>
					<input type="checkbox" v-model="useInstanceMesh" />
					Use Instanced Mesh
				</label>
			</div>

			<!-- Selected Trader Info -->
			<div class="control-group" v-if="selectedTrader">
				<span>Selected Trader: {{ selectedTrader }}</span>
			</div>
		</div>

		<!-- Three.js Container -->
		<div ref="threeJsContainer" class="threejs-container"></div>

		<!-- Tooltip -->
		<div
			class="tooltip"
			v-if="tooltip.visible"
			:style="{ top: tooltip.y + 'px', left: tooltip.x + 'px' }"
		>
			{{ tooltip.content }}
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from "vue";
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";

// Data Structures
type RoundData = {
	round_number: number;
	round_data: Array<{ volume: number; trader: string }>;
};

type TraderData = {
	trader: string;
	volumes: number[]; // Volume per round, including round 0
};

// Initial Data with Round 0, Round 1, and Round 2
const data = ref<RoundData[]>([
	{
		round_number: 0,
		round_data: [
			{ volume: 0, trader: "t1" },
			{ volume: 0, trader: "t2" },
			{ volume: 0, trader: "t3" },
			{ volume: 0, trader: "t4" },
			{ volume: 0, trader: "t5" },
			{ volume: 0, trader: "t6" },
			{ volume: 0, trader: "t7" },
			{ volume: 0, trader: "t8" },
			{ volume: 0, trader: "t9" },
			{ volume: 0, trader: "t10" },
		],
	},
	{
		round_number: 1,
		round_data: [
			{ volume: 50, trader: "t1" },
			{ volume: 50, trader: "t2" },
			{ volume: 50, trader: "t3" },
			{ volume: 50, trader: "t4" },
			{ volume: 50, trader: "t5" },
			{ volume: 50, trader: "t6" },
			{ volume: 50, trader: "t7" },
			{ volume: 50, trader: "t8" },
			{ volume: 50, trader: "t9" },
			{ volume: 50, trader: "t10" },
		],
	},
	{
		round_number: 2,
		round_data: [
			{ volume: 40, trader: "t1" },
			{ volume: 35, trader: "t2" },
			{ volume: 42, trader: "t3" },
			{ volume: 41, trader: "t4" },
			{ volume: 20, trader: "t5" },
			{ volume: 25, trader: "t6" },
			{ volume: 35, trader: "t7" },
			{ volume: 33, trader: "t8" },
			{ volume: 34, trader: "t9" },
			{ volume: 12, trader: "t10" },
		],
	},
]);

// Initialize Traders Data
const traders = ref<TraderData[]>([
	{ trader: "t1", volumes: [] },
	{ trader: "t2", volumes: [] },
	{ trader: "t3", volumes: [] },
	{ trader: "t4", volumes: [] },
	{ trader: "t5", volumes: [] },
	{ trader: "t6", volumes: [] },
	{ trader: "t7", volumes: [] },
	{ trader: "t8", volumes: [] },
	{ trader: "t9", volumes: [] },
	{ trader: "t10", volumes: [] },
]);

// Populate Initial Volumes
data.value.forEach((round) => {
	round.round_data.forEach((entry) => {
		const trader = traders.value.find((t) => t.trader === entry.trader);
		trader?.volumes.push(entry.volume);
	});
});

// Reactive UI Controls
const useSlope = ref(false);
const selectedMaterial = ref("basic"); // 'basic', 'standard', 'lambert'
const selectedTexture = ref("none"); // 'none', 'grid', 'checker'
const ribbonWidth = ref(2); // Increased depth
const ribbonThickness = ref(0.1); // Decreased height
const ribbonSeparation = ref(2); // Separation between traders
const useInstanceMesh = ref(false);

// Tooltip State
const tooltip = ref({
	visible: false,
	content: "",
	x: 0,
	y: 0,
});

// Selected Trader
const selectedTrader = ref<string | null>(null);

// Three.js Setup
const threeJsContainer = ref<HTMLElement | null>(null);

let scene: THREE.Scene;
let camera: THREE.PerspectiveCamera;
let renderer: THREE.WebGLRenderer;
let controls: OrbitControls;
let traderGroups: Map<string, THREE.Group> = new Map();
let raycaster = new THREE.Raycaster();
let mouse = new THREE.Vector2();
let intersects: THREE.Intersection[] = [];

// Assign unique colors to traders
const traderColors = [
	0xff5733, // t1
	0x33ff57, // t2
	0x3357ff, // t3
	0xff33a8, // t4
	0xa833ff, // t5
	0x33fff6, // t6
	0xff8f33, // t7
	0x8fff33, // t8
	0x338fff, // t9
	0xff3333, // t10
];

// Texture Loader
const textureLoader = new THREE.TextureLoader();
const textures: { [key: string]: THREE.Texture | null } = {
	grid: null,
	checker: null,
	none: null,
};

// Load Textures
const loadTextures = () => {
	if (selectedTexture.value === "grid" && !textures.grid) {
		textures.grid = textureLoader.load(
			"https://threejs.org/examples/textures/grid.png"
		);
		textures.grid.wrapS = textures.grid.wrapT = THREE.RepeatWrapping;
		textures.grid.repeat.set(4, 4);
	}
	if (selectedTexture.value === "checker" && !textures.checker) {
		textures.checker = textureLoader.load(
			"https://threejs.org/examples/textures/checker.png"
		);
		textures.checker.wrapS = textures.checker.wrapT = THREE.RepeatWrapping;
		textures.checker.repeat.set(4, 4);
	}
};

// Create Material based on selection
const getMaterial = (color: number): THREE.Material => {
	let material: THREE.Material;
	switch (selectedMaterial.value) {
		case "standard":
			material = new THREE.MeshStandardMaterial({ color });
			break;
		case "lambert":
			material = new THREE.MeshLambertMaterial({ color });
			break;
		default:
			material = new THREE.MeshBasicMaterial({ color });
	}

	if (selectedTexture.value !== "none" && textures[selectedTexture.value]) {
		(material as THREE.MeshStandardMaterial).map =
			textures[selectedTexture.value];
		(material as THREE.MeshStandardMaterial).needsUpdate = true;
	}

	return material;
};

// Create Trader Ribbons
const createTraderRibbons = () => {
	traders.value.forEach((trader, index) => {
		const group = new THREE.Group();
		group.position.z = index * (ribbonWidth.value + ribbonSeparation.value);
		scene.add(group);
		traderGroups.set(trader.trader, group);

		const color = traderColors[index % traderColors.length];

		// Initial Ribbon Construction starting from Round 1
		// Skip Round 0 to avoid initial vertical step
		for (let i = 1; i < trader.volumes.length; i++) {
			const currentVolume = trader.volumes[i];
			const previousVolume = trader.volumes[i - 1];
			addRibbonStep(group, i, previousVolume, currentVolume, color);
		}
	});
};

// Add Ribbon Step as Vertical and Horizontal Boxes or Sloped
const addRibbonStep = (
	group: THREE.Group,
	roundIndex: number,
	previousVolume: number,
	currentVolume: number,
	color: number
) => {
	const x = roundIndex;

	// Add vertical or sloped step
	if (roundIndex > 1) {
		let stepGeometry: THREE.BoxGeometry | THREE.CylinderGeometry;
		let stepMaterial: THREE.Material;

		if (useSlope.value) {
			// Create a slope using a box rotated
			const delta = currentVolume - previousVolume;
			const slopeHeight = Math.abs(delta);
			const slopeGeometry = new THREE.BoxGeometry(
				1,
				slopeHeight,
				ribbonWidth.value
			);
			stepMaterial = getMaterial(color);
			const slopeBox = new THREE.Mesh(slopeGeometry, stepMaterial);
			slopeBox.rotation.z = delta < 0 ? Math.PI / 4 : -Math.PI / 4;
			slopeBox.position.set(
				x,
				previousVolume + delta / 2,
				0
			);
			group.add(slopeBox);
		} else {
			// Create a vertical step
			const verticalGeometry = new THREE.BoxGeometry(
				ribbonThickness.value, // Width along X-axis
				Math.abs(currentVolume - previousVolume), // Height along Y-axis
				ribbonWidth.value // Depth along Z-axis
			);
			stepMaterial = getMaterial(color);
			const verticalBox = new THREE.Mesh(verticalGeometry, stepMaterial);
			verticalBox.position.set(
				x,
				previousVolume + (currentVolume - previousVolume) / 2,
				0
			);
			group.add(verticalBox);
		}
	}

	// Add horizontal step
	const horizontalGeometry = new THREE.BoxGeometry(
		1, // Width along X-axis
		ribbonThickness.value, // Height along Y-axis
		ribbonWidth.value // Depth along Z-axis
	);
	const horizontalMaterial = getMaterial(color);
	const horizontalBox = new THREE.Mesh(horizontalGeometry, horizontalMaterial);
	horizontalBox.position.set(x + 0.5, currentVolume, 0);
	group.add(horizontalBox);
};

// Update Ribbons with New Round
const updateRibbons = (newRound: RoundData) => {
	newRound.round_data.forEach((entry) => {
		const trader = traders.value.find((t) => t.trader === entry.trader);
		if (trader) {
			const previousVolume =
				trader.volumes.length > 0
					? trader.volumes[trader.volumes.length - 1]
					: 0;
			trader.volumes.push(entry.volume);
			const group = traderGroups.get(trader.trader);
			if (group) {
				const traderIndex = traders.value.findIndex(
					(t) => t.trader === trader.trader
				);
				const color = traderColors[traderIndex % traderColors.length];
				addRibbonStep(
					group,
					trader.volumes.length - 1,
					previousVolume,
					entry.volume,
					color
				);
			}
		}
	});
};

// Initialize Three.js
const initThreeJS = () => {
	if (!threeJsContainer.value) return;

	// Scene
	scene = new THREE.Scene();
	scene.background = new THREE.Color(0xf0f0f0);

	// Camera
	const aspect =
		threeJsContainer.value.clientWidth / threeJsContainer.value.clientHeight;
	camera = new THREE.PerspectiveCamera(45, aspect, 0.1, 1000);
	camera.position.set(30, 50, 60);
	camera.lookAt(0, 0, 0);

	// Renderer
	renderer = new THREE.WebGLRenderer({ antialias: true });
	renderer.setSize(
		threeJsContainer.value.clientWidth,
		threeJsContainer.value.clientHeight
	);
	threeJsContainer.value.appendChild(renderer.domElement);

	// Controls
	controls = new OrbitControls(camera, renderer.domElement);
	controls.enableDamping = true;
	controls.dampingFactor = 0.05;
	controls.minDistance = 10;
	controls.maxDistance = 200;
	controls.update();

	// Lights
	const ambientLight = new THREE.AmbientLight(0x404040, 2);
	scene.add(ambientLight);

	const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
	directionalLight.position.set(50, 50, 50);
	scene.add(directionalLight);

	// Helpers
	const axesHelper = new THREE.AxesHelper(50);
	scene.add(axesHelper);

	// Load Textures
	loadTextures();

	// Create Ribbons
	createTraderRibbons();

	// Event Listeners for Interaction
	window.addEventListener("mousemove", onMouseMove);
	window.addEventListener("click", onMouseClick);

	animate();
};

// Animation Loop
const animate = () => {
	requestAnimationFrame(animate);
	controls.update();
	renderer.render(scene, camera);
};

// Handle Window Resize
const onWindowResize = () => {
	if (!threeJsContainer.value) return;
	const width = threeJsContainer.value.clientWidth;
	const height = threeJsContainer.value.clientHeight;
	camera.aspect = width / height;
	camera.updateProjectionMatrix();
	renderer.setSize(width, height);
};

// Mouse Move for Tooltip
const onMouseMove = (event: MouseEvent) => {
	if (!threeJsContainer.value) return;

	// Calculate mouse position in normalized device coordinates (-1 to +1) for both components.
	mouse.x =
		((event.clientX - threeJsContainer.value.getBoundingClientRect().left) /
			threeJsContainer.value.clientWidth) *
		2 -
		1;
	mouse.y =
		-(
			(event.clientY - threeJsContainer.value.getBoundingClientRect().top) /
			threeJsContainer.value.clientHeight
		) *
		2 +
		1;

	raycaster.setFromCamera(mouse, camera);
	intersects = raycaster.intersectObjects(scene.children, true);

	if (intersects.length > 0) {
		const intersect = intersects[0];
		const traderGroup = Array.from(traderGroups.values()).find((group) =>
			group.children.includes(intersect.object)
		);
		if (traderGroup) {
			const traderName = Array.from(traderGroups.entries()).find(
				([, group]) => group === traderGroup
			)?.[0];
			tooltip.value = {
				visible: true,
				content: `Trader: ${traderName}`,
				x: event.clientX + 10,
				y: event.clientY + 10,
			};
		} else {
			tooltip.value.visible = false;
		}
	} else {
		tooltip.value.visible = false;
	}
};

// Mouse Click for Selection
const onMouseClick = (event: MouseEvent) => {
	if (!threeJsContainer.value) return;

	// Calculate mouse position in normalized device coordinates (-1 to +1) for both components.
	mouse.x =
		((event.clientX - threeJsContainer.value.getBoundingClientRect().left) /
			threeJsContainer.value.clientWidth) *
		2 -
		1;
	mouse.y =
		-(
			(event.clientY - threeJsContainer.value.getBoundingClientRect().top) /
			threeJsContainer.value.clientHeight
		) *
		2 +
		1;

	raycaster.setFromCamera(mouse, camera);
	intersects = raycaster.intersectObjects(scene.children, true);

	if (intersects.length > 0) {
		const intersect = intersects[0];
		const traderGroup = Array.from(traderGroups.values()).find((group) =>
			group.children.includes(intersect.object)
		);
		if (traderGroup) {
			const traderName = Array.from(traderGroups.entries()).find(
				([, group]) => group === traderGroup
			)?.[0];
			selectedTrader.value = traderName || null;
			highlightTrader(traderName);
		} else {
			selectedTrader.value = null;
			resetHighlights();
		}
	} else {
		selectedTrader.value = null;
		resetHighlights();
	}
};

// Highlight Selected Trader
const highlightTrader = (traderName: string | undefined) => {
	traders.value.forEach((trader) => {
		const group = traderGroups.get(trader.trader);
		if (group) {
			group.children.forEach((child) => {
				if (trader.trader === traderName) {
					(child.material as THREE.MeshStandardMaterial).emissive.setHex(0x555555);
				} else {
					(child.material as THREE.MeshStandardMaterial).emissive.setHex(0x000000);
				}
			});
		}
	});
};

// Reset Highlights
const resetHighlights = () => {
	traders.value.forEach((trader) => {
		const group = traderGroups.get(trader.trader);
		if (group) {
			group.children.forEach((child) => {
				(child.material as THREE.MeshStandardMaterial).emissive.setHex(0x000000);
			});
		}
	});
};

// Watchers for UI Controls
watch(
	[selectedMaterial, selectedTexture],
	() => {
		loadTextures();
		rebuildRibbons();
	},
	{ immediate: true }
);

watch(
	[ribbonWidth, ribbonThickness, ribbonSeparation, useSlope],
	() => {
		rebuildRibbons();
	}
);

watch(useInstanceMesh, () => {
	rebuildRibbons();
});

// Rebuild Ribbons based on current settings
const rebuildRibbons = () => {
	// Remove existing trader groups
	traderGroups.forEach((group) => {
		scene.remove(group);
	});
	traderGroups.clear();

	if (useInstanceMesh.value) {
		// Implement Instanced Mesh (Advanced)
		createTraderRibbonsInstance();
	} else {
		createTraderRibbons();
	}
};

// Create Trader Ribbons with Instanced Mesh
const createTraderRibbonsInstance = () => {
	traders.value.forEach((trader, index) => {
		const color = traderColors[index % traderColors.length];
		const material = getMaterial(color);
		const steps = trader.volumes.length - 1; // Number of steps

		// Calculate total number of instances (steps * 2: vertical and horizontal)
		const instanceCount = steps * 2;

		// Geometry for both vertical and horizontal
		const geometry = new THREE.BoxGeometry(1, 1, ribbonWidth.value);

		const instancedMesh = new THREE.InstancedMesh(
			geometry,
			material,
			instanceCount
		);

		let dummy = new THREE.Object3D();
		let instanceIndex = 0;

		for (let i = 1; i < trader.volumes.length; i++) {
			const currentVolume = trader.volumes[i];
			const previousVolume = trader.volumes[i - 1];
			const x = i;

			// Add vertical or sloped step
			if (useSlope.value) {
				const delta = currentVolume - previousVolume;
				const slopeHeight = Math.abs(delta);
				dummy.position.set(
					x,
					previousVolume + delta / 2,
					0
				);
				dummy.rotation.z = delta < 0 ? Math.PI / 4 : -Math.PI / 4;
				dummy.scale.set(1, slopeHeight, ribbonWidth.value);
				dummy.updateMatrix();
				instancedMesh.setMatrixAt(instanceIndex++, dummy.matrix);
			} else {
				if (i > 1) {
					const delta = currentVolume - previousVolume;
					dummy.position.set(
						x,
						previousVolume + delta / 2,
						0
					);
					dummy.scale.set(
						ribbonThickness.value,
						Math.abs(delta),
						ribbonWidth.value
					);
					dummy.updateMatrix();
					instancedMesh.setMatrixAt(instanceIndex++, dummy.matrix);
				}
			}

			// Add horizontal step
			dummy.position.set(x + 0.5, currentVolume, 0);
			dummy.scale.set(1, ribbonThickness.value, ribbonWidth.value);
			dummy.updateMatrix();
			instancedMesh.setMatrixAt(instanceIndex++, dummy.matrix);
		}

		instancedMesh.instanceMatrix.needsUpdate = true;
		scene.add(instancedMesh);
		traderGroups.set(trader.trader, new THREE.Group()); // Placeholder
	});
};

// Tooltips and Selection handled via event listeners

// Initialize Three.js on Mount
onMounted(() => {
	initThreeJS();
	window.addEventListener("resize", onWindowResize);
});

// Clean up on Unmount
onUnmounted(() => {
	window.removeEventListener("resize", onWindowResize);
	window.removeEventListener("mousemove", onMouseMove);
	window.removeEventListener("click", onMouseClick);
	if (renderer) {
		renderer.dispose();
	}
});
</script>

<style scoped>
.container {
	position: relative;
	width: 100%;
	height: 100vh;
}

.controls {
	position: absolute;
	top: 10px;
	left: 10px;
	background: rgba(255, 255, 255, 0.8);
	padding: 10px;
	border-radius: 8px;
	z-index: 10;
	max-width: 300px;
}

.control-group {
	margin-bottom: 10px;
}

.control-group span {
	display: block;
	margin-bottom: 5px;
	font-weight: bold;
}

.control-group label {
	display: block;
}

.threejs-container {
	width: 100%;
	height: 100%;
	display: block;
}

.tooltip {
	position: absolute;
	background: rgba(0, 0, 0, 0.7);
	color: #ffffff;
	padding: 5px 10px;
	border-radius: 4px;
	pointer-events: none;
	font-size: 12px;
	z-index: 20;
}
</style>
