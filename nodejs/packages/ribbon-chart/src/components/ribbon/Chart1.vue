<!-- from ThreeJSOpusRibbonTube -->

<template>
  <div>
    <canvas ref="canvas"></canvas>
    <DatGui :open>
      <PerspectiveCameraControls :open/>
      <HelperControls :open/>
      <LightControls :light="ambientLight" :open/>
      <!--      <TubeControls :paths="[path]" @meshes-created="addMeshesToScene"/>-->
    </DatGui>
  </div>
</template>

<script setup lang="ts">
import {onMounted, provide, ref, render} from "vue";
import * as THREE from 'three';
import {DatGui} from "@cyrilf/vue-dat-gui";

import HelperControls from "../controls/HelperControls.vue";
import {useThreeOpus} from "../../composables/useThreeOpus";
import LightControls from "../controls/LightControls.vue";
import PerspectiveCameraControls
  from "../controls/PerspectiveCameraControls.vue";
import {Color, GridHelper, Vector3} from "three";


const canvas = ref<HTMLCanvasElement | null>(null);
const {scene, camera, orbitControls, addAnimateFunction} = useThreeOpus(canvas)
const open = ref(false)

// const {scene, camera, orbitControls} = useThreeJSGPT4o(canvas)

provide('scene', scene);
provide('camera', camera);
provide('orbitControls', orbitControls);

const ambientLight = new THREE.AmbientLight(0xffffff, 1);
//scene.add(ambientLight)

scene.add(new GridHelper(10, 10));
camera.zoom = 4
camera.position.y = 20


// Define colors for each series
const colors = {
  'Series A': 0x00ff00,
  'Series B': 0x0000ff,
  'Series C': 0xff00ff
};


function createVerticalLine() {
  const geometry = new THREE.PlaneGeometry(10, 0.1);
  const material = new THREE.MeshBasicMaterial({
    color: 0x1F4365,
    side: THREE.DoubleSide
  });
  const plane = new THREE.Mesh(geometry, material);
  plane.rotateZ(Math.PI / 2)
  plane.translateY(5)
  addAnimateFunction(() => {
    const cameraPos = new THREE.Vector3();
    camera.getWorldPosition(cameraPos); // Get the world position of the camera
    const direction = cameraPos.sub(plane.position).normalize(); // Direction from plane to camera
    const angle = Math.atan2(direction.x, direction.z); // Calculate the angle for y-axis rotation
    plane.rotation.y = angle; // Rotate the plane around y-axis
  })
  return plane
}

function createHorizontalLine() {
  const geometry = new THREE.PlaneGeometry(10, 0.1);  // Length 10, thickness 0.1
  const material = new THREE.MeshBasicMaterial({
    color: 0x1F4365,
    side: THREE.DoubleSide
  });
  const plane = new THREE.Mesh(geometry, material);

  // Add the line to the scene (assuming scene is globally accessible)
  scene.add(plane);

  // Function to adjust the rotation of the line to always face the camera
  addAnimateFunction(() => {
    const cameraPos = new THREE.Vector3();
    camera.getWorldPosition(cameraPos);  // Get the world position of the camera

    // Calculate rotation around x-axis
    const direction = cameraPos.sub(plane.position).normalize();
    const angleX = Math.atan2(direction.y, Math.sqrt(direction.x * direction.x + direction.z * direction.z));

    // Set rotation to face the camera
    plane.rotation.x = -angleX;  // Adjust x-axis rotation to handle vertical angle
  });

  return plane;
}

function createVerticalCylinder() {
  const geometry = new THREE.CylinderGeometry(0.05, 0.05, 10, 32);  // Small radius for a line-like appearance, height 10
  const material = new THREE.MeshBasicMaterial({color: 0x1F4365});
  const cylinder = new THREE.Mesh(geometry, material);
  return cylinder;
}

function createHorizontalCylinder() {
  const geometry = new THREE.CylinderGeometry(0.05, 0.05, 10, 32);  // Small radius for a line-like appearance, length 10
  const material = new THREE.MeshBasicMaterial({color: 0x1F4365});
  const cylinder = new THREE.Mesh(geometry, material);

  // Rotate the cylinder to make it horizontal
  cylinder.rotation.x = Math.PI / 2;  // Rotate 90 degrees around the x-axis

  // Add the cylinder to the scene (assuming scene is globally accessible)
  scene.add(cylinder);

  return cylinder;
}

function createCylinder(
    length: number,
    isVertical: boolean,
    position: Vector3,
    color: THREE.ColorRepresentation
) {
  const radius = 0.05;
  const segments = 32;
  const geometry = new THREE.CylinderGeometry(radius, radius, length, segments);
  const material = new THREE.MeshBasicMaterial({color: color});
  const cylinder = new THREE.Mesh(geometry, material);

  if (!isVertical) {
    // Rotate to make it vertical
    cylinder.rotation.z = Math.PI / 2;
  }
  // Set the position
  cylinder.position.set(position.x, position.y, position.z);
  return cylinder
}

// Lengths of the box edges
const width = 10;
const height = 10;
const depth = 10;

// Create horizontal cylinders (edges)
// front:
const front = createCylinder(width, false, new THREE.Vector3(0, 0, 5),
    0x1F4365);

addAnimateFunction(() => {
  const controls = orbitControls()
  if (controls) {
    const polarAngle = controls.getPolarAngle();
    //console.log(polarAngle)
    // Check if the polar angle is greater than a threshold value (e.g., 2.9 radians)
    front.visible = polarAngle > 0
    // Update the OrbitControls
    controls.update();
  }
})

scene.add(front)
// Bottom front
//createCylinder(width, false, new THREE.Vector3(width / 2, height, 0), 0x1F4365);  // Top front
//createCylinder(depth, false, new THREE.Vector3(0, 0, depth / 2), 0x1F4365);
// Bottom left
//createCylinder(depth, false, new THREE.Vector3(0, height, depth / 2),0x1F4365);  // Top left

// Create vertical cylinders (edges)
//createCylinder(height, true, new THREE.Vector3(0, height / 2, 0), 0x1F4365);
// Front left
//createCylinder(height, true, new THREE.Vector3(width, height / 2, 0),0x1F4365);  // Front right
//createCylinder(height, true, new THREE.Vector3(0, height / 2, depth),0x1F4365);  // Back left

//scene.add(createVerticalLine());
// scene.add(createHorizontalLine());
//scene.add(createVerticalCylinder())
//scene.add(createHorizontalCylinder())

// Set up the camera position and render the scene
//camera.position.set(30, 20, 50);
//camera.lookAt(25, -2, 0);

</script>


