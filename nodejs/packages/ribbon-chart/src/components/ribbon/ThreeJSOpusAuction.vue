<template>
  <canvas ref="experience"></canvas>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";
import {mockAuction} from "../../stores/auction-data";
const experience = ref<HTMLCanvasElement | null>(null);
const {scene, camera} = useThreeJSGPT4o(experience)
import * as THREE from 'three';

// Create a group to hold the ribbon objects
const ribbonGroup = new THREE.Group();

// Define the colors for each trader
const traderColors: { [traderId: string]: THREE.Color } = {
  A: new THREE.Color('red'),
  B: new THREE.Color('blue'),
  C: new THREE.Color('green'),
  D: new THREE.Color('orange'),
  E: new THREE.Color('purple'),
};

// ---- this is wrong, orders are part of rounds not auction: ---
// Create ribbons for each trader
// mockAuction.orders.forEach((trader, traderIndex) => {
//   const points: THREE.Vector2[] = [];
//
//   // Add points for each round
//   mockAuction.rounds.forEach((round, roundIndex) => {
//     const traderData = round.traderOrders.find((t) => t.traderId === trader.traderId);
//     if (traderData) {
//       points.push(new THREE.Vector2(roundIndex, traderData.buyVolume));
//       points.push(new THREE.Vector2(roundIndex, traderData.sellVolume));
//       if (roundIndex < mockAuction.rounds.length - 1) {
//         points.push(new THREE.Vector2(roundIndex + 1, traderData.sellVolume));
//       }
//     }
//   });
//
//   const shape = new THREE.Shape();
//   shape.moveTo(points[0].x, points[0].y);
//   for (let i = 1; i < points.length; i++) {
//     shape.lineTo(points[i].x, points[i].y);
//   }
//
//   const extrudeSettings = {
//     steps: 1,
//     depth: 0.1,
//     bevelEnabled: false,
//   };
//
//   const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
//   const material = new THREE.MeshBasicMaterial({
//     color: traderColors[trader.traderId],
//     side: THREE.DoubleSide,
//   });
//   const mesh = new THREE.Mesh(geometry, material);
//
//   // Set the z-position based on the trader index
//   mesh.position.z = traderIndex * 0.5;
//
//   ribbonGroup.add(mesh);
// });
//
// // Add the ribbon group to the scene
// scene.add(ribbonGroup);
</script>

