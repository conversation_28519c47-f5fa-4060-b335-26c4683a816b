<template>
	<div>
		<div id="controls">
			<h3>Controls</h3>
			<label for="colorscale">Colorscale:</label>
			<select id="colorscale" v-model="colorscale">
				<option value="Viridis">Viridis</option>
				<option value="Cividis">Cividis</option>
				<option value="Hot">Hot</option>
				<option value="Electric">Electric</option>
				<option value="Earth">Earth</option>
			</select>
			<br />
			<label for="opacity">Opacity:</label>
			<input type="range" id="opacity" min="0" max="1" step="0.1" v-model="opacity" />
			<span>{{ opacity }}</span>
			<br />
			<label for="ambient">Ambient Lighting:</label>
			<input type="range" id="ambient" min="0" max="1" step="0.1" v-model="ambient" />
			<span>{{ ambient }}</span>
			<br />
			<label for="diffuse">Diffuse Lighting:</label>
			<input type="range" id="diffuse" min="0" max="1" step="0.1" v-model="diffuse" />
			<span>{{ diffuse }}</span>
			<br />
			<label for="rotationLimit">Rotation Limit (°):</label>
			<input type="range" id="rotationLimit" min="0" max="360" step="10" v-model="rotationLimit" />
			<span>{{ rotationLimit }}</span>
			<br /><br />
			<button @click="resetCamera">Reset Camera View</button>
		</div>
		<div id="myDiv"></div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import Plotly, { Layout } from "plotly.js-dist"

const colorscale = ref('Viridis');
const opacity = ref(1);
const ambient = ref(0.5);
const diffuse = ref(0.5);
const rotationLimit = ref(360);

let data: any[] = [];
let layout: any = {};
let animationInterval: any;
let figureData: any;
let rotating = false;
const maxIterations = 10;
let animationCount = 0;
let relayoutHandler: any;

const initPlot = () => {
	data = [];


	const maxPoints = 50; // Limit the number of points

	for (let i = 0; i < 7; i++) {
		const trace = {
			x: figureData[i].x.slice(0, maxPoints),
			y: figureData[i].y.slice(0, maxPoints),
			z: figureData[i].z.slice(0, maxPoints).map((row: any) => row.slice(0, maxPoints)),
			name: '',
			colorscale: colorscale.value,
			opacity: opacity.value,
			lighting: {
				ambient: ambient.value,
				diffuse: diffuse.value,
				specular: 0.1,
				roughness: 0.9,
				fresnel: 0.2,
			},
			type: 'surface',
			showscale: false,
		};
		data.push(trace);
	}

	layout = {
		title: 'Ribbon Plot',
		showlegend: false,
		autosize: true,
		scene: {
			xaxis: { title: 'Sample #' },
			yaxis: { title: 'Wavelength' },
			zaxis: { title: 'OD' },
			camera: {
				eye: { x: 1.5, y: 1.5, z: 1 },
				up: { x: 0, y: 0, z: 1 },
			},
			dragmode: 'turntable',
		},
	};

	Plotly.newPlot('myDiv', data, layout);

	setUpRotationLimit();
};

const setUpRotationLimit = () => {
	const myDiv = document.getElementById('myDiv') as any;

	if (myDiv) {
		if (relayoutHandler) {
			myDiv.removeListener('plotly_relayout', relayoutHandler);
		}

		relayoutHandler = (eventData: any) => {
			if (eventData['scene.camera'] && !rotating) {
				rotating = true;
				const camera = eventData['scene.camera'];
				const eye = camera.eye;
				const r = Math.sqrt(eye.x * eye.x + eye.y * eye.y);
				let theta = Math.atan2(eye.y, eye.x) * (180 / Math.PI);

				const limitAngle = rotationLimit.value / 2;
				if (theta > limitAngle) {
					theta = limitAngle;
				} else if (theta < -limitAngle) {
					theta = -limitAngle;
				}

				const newEyeX = r * Math.cos((theta * Math.PI) / 180);
				const newEyeY = r * Math.sin((theta * Math.PI) / 180);

				const update = {
					'scene.camera.eye.x': newEyeX,
					'scene.camera.eye.y': newEyeY,
				} as Partial<Layout>;

				Plotly.relayout('myDiv', update).then(() => {
					rotating = false;
				});
			}
		};

		myDiv.on('plotly_relayout', relayoutHandler);
	}
};

const updatePlot = () => {
	data.forEach((trace) => {
		trace.colorscale = colorscale.value;
		trace.opacity = opacity.value;
		trace.lighting = {
			ambient: ambient.value,
			diffuse: diffuse.value,
			specular: 0.1,
			roughness: 0.9,
			fresnel: 0.2,
		};
	});

	Plotly.react('myDiv', data, layout);
};

const resetCamera = () => {
	Plotly.relayout('myDiv', {
		'scene.camera': {
			eye: { x: 1.5, y: 1.5, z: 1 },
			up: { x: 0, y: 0, z: 1 },
		},
	} as Partial<Layout>);
};

const startAnimation = () => {
	animationInterval = setInterval(() => {
		for (let i = 0; i < data.length; i++) {
			for (let j = 0; j < data[i].z.length; j++) {
				for (let k = 0; k < data[i].z[j].length; k++) {
					data[i].z[j][k] += 0.1;
				}
			}
		}

		Plotly.animate(
			'myDiv',
			{
				data: data,
			},
			{
				transition: {
					duration: 500,
					easing: 'linear',
				},
				frame: {
					duration: 500,
					redraw: false,
				},
			}
		);

		animationCount++;
		if (animationCount >= maxIterations) {
			clearInterval(animationInterval);
		}
	}, 1000);
};

watch([colorscale, opacity, ambient, diffuse], () => {
	updatePlot();
});

onMounted(async () => {
	const response = await fetch('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json');
	const figure = await response.json();
	figureData = figure.data;

	initPlot();
	startAnimation();
});
</script>

<style scoped>
#controls {
	margin-bottom: 20px;
}

#controls label {
	display: inline-block;
	width: 150px;
}

#controls input,
#controls select {
	margin-bottom: 10px;
}

#myDiv {
	width: 100%;
	height: 600px;
}
</style>
