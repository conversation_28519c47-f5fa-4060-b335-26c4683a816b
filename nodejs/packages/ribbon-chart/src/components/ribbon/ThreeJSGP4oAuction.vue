<template>
  <canvas ref="experience"></canvas>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";
const experience = ref<HTMLCanvasElement | null>(null);

const {scene, camera} = useThreeJSGPT4o(experience)

import * as THREE from 'three';
import {mockAuction} from "../../stores/auction-data";

// Function to create a 3D ribbon chart
function createRibbonChart(scene: THREE.Scene) {
  const buyVolumeMaterial = new THREE.MeshBasicMaterial({ color: 0x00ff00, side: THREE.DoubleSide, transparent: true, opacity: 0.6 });
  const sellVolumeMaterial = new THREE.MeshBasicMaterial({ color: 0xff0000, side: THREE.DoubleSide, transparent: true, opacity: 0.6 });

  const rounds = mockAuction.rounds;
  const numRounds = rounds.length;
  const traderIds = rounds[0].traderOrders.map(trader => trader.traderId);
  const numTraders = traderIds.length;

  // Parameters for positioning
  const roundSpacing = 5;
  const traderSpacing = 2;
  const volumeScale = 0.1;

  rounds.forEach((round, roundIndex) => {
    round.traderOrders.forEach((trader, traderIndex) => {
      const buyVolumeHeight = trader.buyVolume * volumeScale;
      const sellVolumeHeight = trader.sellVolume * volumeScale;
      const xPosition = roundIndex * roundSpacing;
      const zPosition = traderIndex * traderSpacing;

      // Buy Volume Ribbon
      if (buyVolumeHeight > 0) {
        const buyVolumeGeometry = new THREE.PlaneGeometry(roundSpacing, buyVolumeHeight);
        const buyVolumeMesh = new THREE.Mesh(buyVolumeGeometry, buyVolumeMaterial);
        buyVolumeMesh.position.set(xPosition, buyVolumeHeight / 2, zPosition);
        buyVolumeMesh.rotation.x = Math.PI / 2;
        scene.add(buyVolumeMesh);
      }

      // Sell Volume Ribbon
      if (sellVolumeHeight > 0) {
        const sellVolumeGeometry = new THREE.PlaneGeometry(roundSpacing, sellVolumeHeight);
        const sellVolumeMesh = new THREE.Mesh(sellVolumeGeometry, sellVolumeMaterial);
        sellVolumeMesh.position.set(xPosition, -sellVolumeHeight / 2, zPosition);
        sellVolumeMesh.rotation.x = Math.PI / 2;
        scene.add(sellVolumeMesh);
      }
    });
  });
}

createRibbonChart(scene)

</script>

