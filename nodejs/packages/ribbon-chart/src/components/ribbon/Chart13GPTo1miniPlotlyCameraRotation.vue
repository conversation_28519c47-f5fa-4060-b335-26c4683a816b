<template>
	<table>
		<tr>
			<td>
				<button @click="confirmReset">Reset Camera View</button>
				<br>
				<b>
					TODO: user friendly naming:<br> Azimuth = rotation,
					Elevation =
					tilt
					<br>
					Good values:
					<br>
					tilt: 9 to 32 degrees
					<br>
					rotation: 25 to 160 degrees
				</b>

				<div class="controls">
					<h3>Rotation Limits</h3>
					<div class="control-group">
						<label for="minElevation">Minimum Elevation Angle:
							{{ minPhi }}°</label>
						<input
							id="minElevation"
							type="range"
							min="-90"
							max="90"
							v-model.number="minPhi"
							@input="updateLimits('phi', 'min')"
						/>
						<span class="tooltip">Sets the lowest vertical tilt allowed.</span>
					</div>

					<div class="control-group">
						<label for="maxElevation">Maximum Elevation Angle:
							{{ maxPhi }}°</label>
						<input
							id="maxElevation"
							type="range"
							min="-90"
							max="90"
							v-model.number="maxPhi"
							@input="updateLimits('phi', 'max')"
						/>
						<span class="tooltip">Sets the highest vertical tilt allowed.</span>
					</div>

					<div class="control-group">
						<label for="minAzimuth">Minimum Azimuthal Angle:
							{{ minTheta }}°</label>
						<input
							id="minAzimuth"
							type="range"
							min="-180"
							max="180"
							v-model.number="minTheta"
							@input="updateLimits('theta', 'min')"
						/>
						<span class="tooltip">Sets the farthest left rotation allowed.</span>
					</div>

					<div class="control-group">
						<label for="maxAzimuth">Maximum Azimuthal Angle:
							{{ maxTheta }}°</label>
						<input
							id="maxAzimuth"
							type="range"
							min="-180"
							max="180"
							v-model.number="maxTheta"
							@input="updateLimits('theta', 'max')"
						/>
						<span class="tooltip">Sets the farthest right rotation allowed.</span>
					</div>
				</div>

				<div class="controls">
					<h3>Current Rotation</h3>
					<div class="control-group">
						<label for="currentElevation">Current Elevation Angle:
							{{ currentPhi }}°</label>
						<input
							id="currentElevation"
							type="range"
							:min="minPhi"
							:max="maxPhi"
							v-model.number="currentPhi"
							@input="updateCamera"
						/>
						<span class="tooltip">Adjusts the current vertical tilt of the view.</span>
					</div>

					<div class="control-group">
						<label for="currentAzimuth">Current Azimuthal Angle:
							{{ currentTheta }}°</label>
						<input
							id="currentAzimuth"
							type="range"
							:min="minTheta"
							:max="maxTheta"
							v-model.number="currentTheta"
							@input="updateCamera"
						/>
						<span class="tooltip">Adjusts the current horizontal rotation of the view.</span>
					</div>
				</div>
			</td>
			<td>

				<div ref="plotlyDiv" style="width: 100%; height: 600px;"></div>
			</td>
			<td>
				<div class="camera-info">
					<p><strong>Camera Position:</strong></p>
					<ul>
						<li>X: {{ camera.x.toFixed(2) }}</li>
						<li>Y: {{ camera.y.toFixed(2) }}</li>
						<li>Z: {{ camera.z.toFixed(2) }}</li>
					</ul>
				</div>
			</td>
		</tr>
	</table>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue"
import Plotly, { Layout, PlotData, PlotlyHTMLElement } from "plotly.js-dist"

// Reference to the Plotly div
const plotlyDiv = ref<HTMLElement | null>(null)
let gd: PlotlyHTMLElement | null = null
let data: Array<Partial<PlotData>> = []
let layout: Partial<Layout> = {}

// Initial camera eye position
const initialEye = { x: 1.5, y: 1.5, z: 1 }

// Reactive variables for rotation limits
const minPhi = ref(-20)
const maxPhi = ref(60)
const minTheta = ref(-120)
const maxTheta = ref(120)

// Reactive variables for current rotation angles
const currentPhi = ref(
	Math.asin(initialEye.z / Math.sqrt(initialEye.x ** 2 + initialEye.y ** 2 + initialEye.z ** 2)) * (180 / Math.PI)
)
const currentTheta = ref(Math.atan2(initialEye.y, initialEye.x) * (180 / Math.PI))

// Reactive variable for camera position
const camera = ref({ x: initialEye.x, y: initialEye.y, z: initialEye.z })

// Function to update camera based on currentPhi and currentTheta
const updateCamera = () => {
	if (!gd) return

	// Ensure currentPhi and currentTheta are within limits
	currentPhi.value = Math.max(minPhi.value, Math.min(maxPhi.value, currentPhi.value))
	currentTheta.value = Math.max(minTheta.value, Math.min(maxTheta.value, currentTheta.value))

	const r = Math.sqrt(initialEye.x ** 2 + initialEye.y ** 2 + initialEye.z ** 2)
	const phiRad = currentPhi.value * (Math.PI / 180)
	const thetaRad = currentTheta.value * (Math.PI / 180)

	const newEye = {
		x: r * Math.cos(phiRad) * Math.cos(thetaRad),
		y: r * Math.cos(phiRad) * Math.sin(thetaRad),
		z: r * Math.sin(phiRad)
	}

	// Update camera position in reactive variable
	camera.value = { ...newEye }

	Plotly.relayout(gd, {
		"scene.camera.eye": newEye,
		"scene.camera.transition": {
			duration: 500,
			easing: "cubic-in-out"
		}
	})
}

// Function to reset camera to initial position and limits
const resetCamera = () => {
	if (!gd) return

	// Reset rotation limits to initial values
	minPhi.value = -20
	maxPhi.value = 60
	minTheta.value = -120
	maxTheta.value = 120

	// Reset current rotation angles
	currentPhi.value =
		Math.asin(initialEye.z / Math.sqrt(initialEye.x ** 2 + initialEye.y ** 2 + initialEye.z ** 2)) * (180 / Math.PI)
	currentTheta.value = Math.atan2(initialEye.y, initialEye.x) * (180 / Math.PI)

	// Reset camera position
	camera.value = { ...initialEye }

	Plotly.relayout(gd, {
		"scene.camera.eye": initialEye,
		"scene.camera.transition": {
			duration: 500,
			easing: "cubic-in-out"
		}
	})
}

// Function to confirm reset action
const confirmReset = () => {
	if (confirm("Are you sure you want to reset the camera view and rotation limits to default?")) {
		resetCamera()
	}
}

// Function to initialize the Plotly plot
const initPlot = (figure: any) => {
	if (!plotlyDiv.value) return

	data = figure.data.map((trace: any, index: number) => ({
		x: trace.x.slice(0, 50),
		y: trace.y.slice(0, 50),
		z: trace.z.slice(0, 50).map((row: any) => row.slice(0, 50)),
		name: `Trace ${index + 1}`,
		type: "surface",
		showscale: false
	}))

	layout = {
		title: "Ribbon Plot",
		scene: {
			xaxis: { title: "Sample #" },
			yaxis: { title: "Wavelength" },
			zaxis: { title: "OD" },
			camera: {
				eye: initialEye,
				up: { x: 0, y: 0, z: 1 }
			},
			dragmode: false // Disable user rotation
		}
	}

	Plotly.newPlot(plotlyDiv.value, data, layout).then((plotlyInstance) => {
		gd = plotlyInstance
	})
}

// Function to update rotation limits based on slider changes
const updateLimits = (axis: "phi" | "theta", limit: "min" | "max") => {
	if (axis === "phi") {
		if (limit === "min" && minPhi.value > maxPhi.value) {
			maxPhi.value = minPhi.value
		}
		if (limit === "max" && maxPhi.value < minPhi.value) {
			minPhi.value = maxPhi.value
		}
	} else if (axis === "theta") {
		if (limit === "min" && minTheta.value > maxTheta.value) {
			maxTheta.value = minTheta.value
		}
		if (limit === "max" && maxTheta.value < minTheta.value) {
			minTheta.value = maxTheta.value
		}
	}

	// After updating limits, ensure current angles are within new ranges
	currentPhi.value = Math.max(minPhi.value, Math.min(maxPhi.value, currentPhi.value))
	currentTheta.value = Math.max(minTheta.value, Math.min(maxTheta.value, currentTheta.value))

	updateCamera()
}

// Watchers to enforce limits when min/max sliders change
watch([minPhi, maxPhi, minTheta, maxTheta], () => {
	// Update camera if current angles are out of new limits
	currentPhi.value = Math.max(minPhi.value, Math.min(maxPhi.value, currentPhi.value))
	currentTheta.value = Math.max(minTheta.value, Math.min(maxTheta.value, currentTheta.value))
	updateCamera()
})

// Watcher to update camera position reactively
watch([currentPhi, currentTheta], () => {
	updateCamera()
})

onMounted(async () => {
	try {
		const response = await fetch("https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json")
		const figure = await response.json()
		initPlot(figure)
	} catch (error) {
		console.error("Error fetching or initializing plot:", error)
	}
})

onUnmounted(() => {
	if (gd) {
		Plotly.purge(gd)
	}
})
</script>

<style scoped>
button {
	margin-bottom: 20px;
	padding: 10px 20px;
	font-size: 16px;
	cursor: pointer;
}

.controls {
	margin-bottom: 20px;
	padding: 15px;
	border: 1px solid #ddd;
	border-radius: 5px;
	background-color: #f9f9f9;
}

.control-group {
	margin-bottom: 20px;
}

label {
	display: block;
	margin-bottom: 5px;
	font-weight: bold;
}

input[type="range"] {
	width: 100%;
}

h3 {
	margin-bottom: 15px;
	color: #333;
}

.tooltip {
	display: block;
	margin-top: 5px;
	font-size: 12px;
	color: #666;
}

.camera-info {
	margin-top: 20px;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 5px;
	background-color: #eef;
}

.camera-info p {
	margin: 0 0 5px 0;
	font-weight: bold;
}

.camera-info ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.camera-info li {
	margin-bottom: 3px;
}
</style>
