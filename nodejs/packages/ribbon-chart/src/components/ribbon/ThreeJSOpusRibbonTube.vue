<template>
  <div>
    <canvas ref="canvas"></canvas>
    <DatGui :is-open="isOpen">
      <PerspectiveCameraControls/>
      <HelperControls :show-initially="false"/>
      <LightControls :light="ambientLight"/>
      <TubeControls :paths="[path]" @meshes-created="addMeshesToScene"/>
    </DatGui>
  </div>
</template>

<script setup lang="ts">
import {onMounted, provide, ref, render} from "vue";
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";
import {
  BoxGeometry, Group,
  Mesh, MeshBasicMaterial, Shape,
  Vector3
} from "three";
import * as THREE from 'three';
import {DatGui} from "@cyrilf/vue-dat-gui";
import PerspectiveCameraControls
  from "../controls/PerspectiveCameraControls.vue";
import HelperControls from "../controls/HelperControls.vue";
import {useThreeOpus} from "../../composables/useThreeOpus";
import LightControls from "../controls/LightControls.vue";
import TubeControls from "../controls/TubeControls.vue";
import {ExtrudeGeometryOptions} from "three/src/geometries/ExtrudeGeometry";

const canvas = ref<HTMLCanvasElement | null>(null);


const isOpen = ref(true)

// const {scene, camera, orbitControls} = useThreeJSGPT4o(canvas)
const {scene, camera, orbitControls} = useThreeOpus(canvas)

provide('scene', scene);
provide('camera', camera);
provide('orbitControls', orbitControls);

const ambientLight = new THREE.AmbientLight(0xffffff, 1);

const data = [
  {x: 0, y: 0},
  {x: 1, y: 0},
  {x: 1, y: 1},
  {x: 2, y: 1},
  {x: 2, y: 2},
  {x: 3, y: 2},
  {x: 3, y: 3},
  {x: 4, y: 3},
  {x: 4, y: 4}
];

// Create an array to store the path vertices
const vertices = [];

const scale = 10

// Iterate over the data points and create the staircase path
for (let i = 0; i < data.length; i++) {
  const point = data[i];

  // Add the current point to the vertices array
  vertices.push(new THREE.Vector3(point.x, point.y, 0));

  // Check if it's not the last point
  if (i < data.length - 1) {
    const nextPoint = data[i + 1];

    // Add an intermediate point with the same y-coordinate as the current point
    vertices.push(new THREE.Vector3(nextPoint.x * scale, point.y * scale, 0));
  }
}
const path = new THREE.CatmullRomCurve3(vertices);

// const geometry = new THREE.TubeGeometry(path, 100, 2, 100, false);
// const tube_scale = {
//   x: 1,
//   y: 1
// }
//
// geometry.scale(tube_scale.x, tube_scale.y, 1);

function addMeshesToScene(meshes: THREE.Mesh[]) {
  meshes.forEach((mesh) => {
    // scene.add(mesh);
  });
}

class CustomSinCurve extends THREE.Curve<Vector3> {

  scale: number

  constructor(scale = 1) {
    super();
    this.scale = scale;
  }

  getPoint(t: any, optionalTarget = new THREE.Vector3()) {

    const tx = t * 3 - 1.5;
    const ty = Math.sin(2 * Math.PI * t);
    const tz = 0;

    return optionalTarget.set(tx, ty, tz).multiplyScalar(this.scale);
  }
}

onMounted(() => {

// xScale and yScale are the scaling factors for the x and y axes


  //const material = new THREE.MeshBasicMaterial({color: 0x00ff00});
  // const mesh = new THREE.Mesh(geometry, material);
  // scene.add(mesh);

  scene.add(ambientLight);

  //camera?.position.set(7, 20, 50);
  //camera?.lookAt(100,100,100)


  /*
   - PLANES
   - BOXES
   - EXTRUDED SHAPES
   - TUBES
   */

  const group = new Group()

  function drawPlane(x, y, z) {
    // RIBBON FROM PLANE:
    const ribbonGeometry = new THREE.PlaneGeometry(1, 1);
    const buyMaterial = new THREE.MeshBasicMaterial({
      color: new THREE.Color(0xcc2244),
      side: THREE.BackSide,
      transparent: false,
      // opacity: 0.7
    });
    // const sellMaterial = new THREE.MeshBasicMaterial({
    //   color: 0xff0000,
    //   side: THREE.DoubleSide,
    //   transparent: true,
    //   opacity: 0.6
    // });

    const buyRibbon = new THREE.Mesh(ribbonGeometry, buyMaterial);
    buyRibbon.position.set(x,  y, z);
    buyRibbon.rotation.x = Math.PI / 2;
    group.add(buyRibbon);
  }

  drawPlane(0.5, 5, 0.5)
  drawPlane(1.5, 3, 0.5)


  const shape1: Shape = ((offsetX:number) => {
    const shape = new THREE.Shape();
    shape.moveTo(0, 0);
    shape.lineTo(1, 0);
    shape.lineTo(1, 5);
    shape.lineTo(0, 5);
    shape.lineTo(0, 0);
    // for (let i = 1; i < points.length; i++) {
    //   shape.lineTo(points[i].x, points[i].y);
    // }
    return shape;
  })(0)

  const shape2: Shape = (() => {
    const shape = new THREE.Shape();
    shape.moveTo(0, 0);
    shape.lineTo(1, 0);
    shape.lineTo(1, 3);
    shape.lineTo(0, 3);
    shape.lineTo(0, 0);
    // for (let i = 1; i < points.length; i++) {
    //   shape.lineTo(points[i].x, points[i].y);
    // }
    return shape;
  })()

  // RIBBON FROM EXTRUDED SHAPE:
  function drawExtrudedShape(shape: Shape, offsetX = 0) {

    const extrudeSettings: ExtrudeGeometryOptions = {
      steps: 1,
      depth: 1,
      bevelEnabled: false,
      // bevelSize: 0.3,
      // bevelThickness: 0.3,
      // bevelSegments: 3,
      // bevelOffset: 0.1,
      // curveSegments: 3
    };

    const geometry = new THREE.ExtrudeGeometry(shape, extrudeSettings);
    const material = new THREE.MeshBasicMaterial({
      color: new THREE.Color(0x882244),
      side: THREE.DoubleSide,
      transparent: true,
      opacity: 0.5,
      wireframe: false,
    });
    const mesh = new THREE.Mesh(geometry, material);

    // Set the z-position based on the trader index
    // mesh.position.z = 0.5;
    //ribbonGroup.add(mesh)
    mesh.translateX(offsetX)
    group.add(mesh)
  }

  drawExtrudedShape(shape1)
  drawExtrudedShape(shape2, 1)

  const cube = new Mesh(
      new BoxGeometry(10, 10, 10, 32, 32),
      new MeshBasicMaterial({color: 0x008080})
  );
  //scene.add(cube);

  // group.rotateY(-Math.PI / 2.5)
  scene.add(group)

});

</script>


