<!-- from ThreeJSOpusRibbonTube -->

<template>
  <div>
    <canvas ref="canvas"></canvas>
    <DatGui :open>
      <PerspectiveCameraControls :open/>
      <HelperControls :open/>
      <LightControls :light="ambientLight" :open/>
      <!--      <TubeControls :paths="[path]" @meshes-created="addMeshesToScene"/>-->
    </DatGui>
  </div>
</template>

<script setup lang="ts">
import {onMounted, provide, ref, render} from "vue";
import {
  BoxGeometry, GridHelper, Group,
  Mesh, MeshBasicMaterial, Plane, Shape,
  Vector3
} from "three";
import * as THREE from 'three';
import {DatGui} from "@cyrilf/vue-dat-gui";
import PerspectiveCameraControls
  from "../controls/PerspectiveCameraControls.vue";
import HelperControls from "../controls/HelperControls.vue";
import {useThreeOpus} from "../../composables/useThreeOpus";
import LightControls from "../controls/LightControls.vue";
import TubeControls from "../controls/TubeControls.vue";
import {ExtrudeGeometryOptions} from "three/src/geometries/ExtrudeGeometry";
import {Auction, Order, Round, Trader} from "../../stores/auction-model";
import {mockAuction} from "../../stores/auction-data";

const canvas = ref<HTMLCanvasElement | null>(null);


const open = ref(false)

// const {scene, camera, orbitControls} = useThreeJSGPT4o(canvas)
const {scene, camera, orbitControls} = useThreeOpus(canvas, false)

provide('scene', scene);
provide('camera', camera);
provide('orbitControls', orbitControls);

const ambientLight = new THREE.AmbientLight(0xffffff, 1);

scene.add(new GridHelper(10, 10));

// Plan:
// --------------------------------------------------------------------
// (1) draw a volume bar group with a plane and a box
//   - plane: these are always the same x, z , just different height, y
//   - bar: again same except for height
//   - combine bar and height in a volume bar group
const plane_material = (color: THREE.Color) => new THREE.MeshBasicMaterial({
  color,
  side: THREE.DoubleSide, // THREE.BackSide,
  transparent: false,
  // opacity: 0.7
});

const buy_plane_material = plane_material(new THREE.Color(0x22cc44))
const sell_plane_material = plane_material(new THREE.Color(0xcc2244))
const zero_plane_material = plane_material(new THREE.Color(0x333333))

const box_material = new THREE.MeshBasicMaterial({
  color: new THREE.Color(0x882244),
  side: THREE.DoubleSide,
  transparent: true,
  opacity: 0.5,
  wireframe: false,
});

const extrudeSettings: ExtrudeGeometryOptions = {
  steps: 1,
  depth: 1,
  bevelEnabled: false,
  // bevelSize: 0.3,
  // bevelThickness: 0.3,
  // bevelSegments: 3,
  // bevelOffset: 0.1,
  // curveSegments: 3
};

// --------------------------------------------------------------------
// (2) trader vol: then draw each trader's vol as a group

function trader_shape(vols: number[]): Mesh {
  //const w = 1
  const s = new THREE.Shape()
  s.moveTo(0, 0)

  vols.forEach((vol, index) => {
    s.lineTo(index, vol)
        .lineTo(index + 1, vol)
  })
  s.lineTo(vols.length, 0)
  s.lineTo(0, 0)

  return new THREE.Mesh(
      new THREE.ExtrudeGeometry(s, extrudeSettings),
      box_material);
}

function trader_ribbon(orders: Order[], pos: number): Group {
  // should planes be extruded lines or shapes???

  const g = new Group()
  orders.forEach((o, index) => {

    if (o.buyVolume > 0 && o.sellVolume > 0) {
      throw new Error("buyVolume and sellVolume can't both be > zero.", o)
    }

    const top = new THREE.Mesh(
        new THREE.PlaneGeometry(1, 1),
        o.buyVolume > 0 ? buy_plane_material : sell_plane_material
    )

    const vol = (o.buyVolume + o.sellVolume) / 10 // one must be zero"
    console.log(vol)
    top.position.set(0.5 + index, vol, 0.5);
    top.rotation.x = Math.PI / 2; // needed to see it, default is flat on the z
    g.add(top)

    function add_plane(start: number, height: number, is_buy: boolean) {
      const side = new THREE.Mesh(
          new THREE.PlaneGeometry(1, height),
          is_buy ? buy_plane_material : sell_plane_material
      )
      side.position.set(index, start + (-0.5 * height), 0.5)
      side.rotation.y = Math.PI / 2
      g.add(side)
    }

    // if (index > 0) {
    //   // does this line cross from buy to sell?
    //   const prev_vol = vols[index - 1]
    //   const delta = vol - prev_vol
    //   add_plane(vol, delta, vol > 0)
    // }
  })
  g.translateZ(2 * pos)
  return g
}

function draw_rounds(a: Auction) {
  a.traders.forEach((t: Trader, pos: number) => {
    if (pos > 0) return
    const trader_orders: Order[] = a.rounds
        .map(r => r.traderOrders
                .find(o => o.traderId === t.traderId)
            || {traderId: t.traderId, sellVolume: 0, buyVolume: 0})

    // console.table(trader_orders)
    scene.add(trader_ribbon(trader_orders, pos))
  })
}

function createVerticalLine(): THREE.Mesh {
  const material = new THREE.MeshBasicMaterial({ color: 0x0000ff });
  const geometry = new THREE.CylinderGeometry(0.05, 0.05, 10, 32); // radiusTop: 0.05, radiusBottom: 0.05, height: 10

  const line = new THREE.Mesh(geometry, material);
  line.position.set(0, 0, 0); // Center the line at the origin
  return line;
}
scene.add(createVerticalLine())

draw_rounds(mockAuction)


// --------------------------------------------------------------------
// (3) trader rounds: then draw all the traders' rounds:


// --------------------------------------------------------------------
// (4) add axes with ticks, and labels sprites?


// --------------------------------------------------------------------
// (5) add round and trader names


// --------------------------------------------------------------------
// (6) consider trader icons


onMounted(() => {

  scene.add(ambientLight);

  /*
   - PLANES
   - BOXES
   - EXTRUDED SHAPES
   - TUBES
   */


  // scene.add(group)
  camera.position.y = 10

});

// WORKS, but we don't need indivitual volume bars:
// function volumeBar(vol: number): Group {
//
//   const top_plane: Mesh = (() => {
//     const m = new THREE.Mesh(
//         new THREE.PlaneGeometry(1, 1),
//         plane_material
//     )
//     m.position.set(0.5, vol, 0.5);
//     m.rotation.x = Math.PI / 2; // needed to see it, default is flat on the z
//     return m
//   })()
//
//   const vol_box: Mesh = (() => {
//     return new THREE.Mesh(new THREE.ExtrudeGeometry(
//             new THREE.Shape()
//                 .moveTo(0, 0)
//                 .lineTo(1, 0)
//                 .lineTo(1, vol)
//                 .lineTo(0, vol)
//                 .lineTo(0, 0)
//             , extrudeSettings),
//         box_material);
//   })()
//
//   return new Group()
//       .add(top_plane)
//       .add(vol_box)
// }
</script>


