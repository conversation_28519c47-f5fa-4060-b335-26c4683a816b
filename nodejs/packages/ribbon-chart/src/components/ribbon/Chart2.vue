<!-- from ThreeJSOpusRibbonTube -->

<template>
  <div>
    <canvas ref="canvas"></canvas>
    <DatGui :open>
      <PerspectiveCameraControls :open/>
      <HelperControls :open/>
      <LightControls :light="ambientLight" :open/>
      <!--      <TubeControls :paths="[path]" @meshes-created="addMeshesToScene"/>-->
    </DatGui>
  </div>
</template>

<script setup lang="ts">
import {onMounted, onUnmounted, provide, ref, render} from "vue";
import * as THREE from 'three';
import {DatGui} from "@cyrilf/vue-dat-gui";

import HelperControls from "../controls/HelperControls.vue";
import {useThreeOpus} from "../../composables/useThreeOpus";
import LightControls from "../controls/LightControls.vue";
import PerspectiveCameraControls
  from "../controls/PerspectiveCameraControls.vue";
import {createChartBax} from "../../helpers/chart/chart-box";
import {drawSpheresAndConnectors} from "../../stores/ribbon-lines";
import {ribbon_data_1, RibbonPoint} from "../../stores/ribbon-series";
import {Group} from "three";
import {mockAuction, to_ribbon_data} from "../../stores/auction-data";

const canvas = ref<HTMLCanvasElement | null>(null);
const {scene, camera, orbitControls, renderer, addAnimateFunction} =
    useThreeOpus(canvas)
const open = ref(false)

provide('scene', scene);
provide('camera', camera);
provide('orbitControls', orbitControls);

const ambientLight = new THREE.AmbientLight(0x404040); // Soft ambient light
scene.add(ambientLight);

const pointLight = new THREE.PointLight(0xffffff); // Bright point light
pointLight.position.set(2, 2, 2);
scene.add(pointLight);

camera.zoom = 0.5
camera.position.set(0, 2.5, 30);

const chart_box =
    createChartBax(
        camera, orbitControls, addAnimateFunction,
        30, 10, 20,
        false)

scene.add(chart_box)

const turquoiseColor = 0x00ffff;
const purpleColor = 0x9400d3;


// Object.entries(ribbon_data_1).forEach(([seriesName, seriesData]) => {
//   console.log(`Series: ${seriesName}`);
//   seriesData.forEach((point, index) => {
//     console.log(`Point ${index}: (${point.x}, ${point.y})`);
//   });
// });
// Usage

const correction = (point: RibbonPoint) => {
  return {x: point.x, y: point.y / 10}
}

function to_ribbon_trader_data(traderId: String): RibbonPoint[] {
  return to_ribbon_data(mockAuction, traderId)[traderId].map(correction)
}

const group = new Group()
    // .add(drawSpheresAndConnectors(ribbon_data_1['Series A'], turquoiseColor, 2.5))
    // .add(drawSpheresAndConnectors(ribbon_data_1['Series B'], purpleColor, 0))
    // .add(drawSpheresAndConnectors(ribbon_data_1['Series C'], 0xff00ff, -2.5))
    .add(drawSpheresAndConnectors(
        to_ribbon_trader_data("A"), turquoiseColor, 2.5))
    .add(drawSpheresAndConnectors(
        to_ribbon_trader_data("B"), purpleColor, 9))
    .add(drawSpheresAndConnectors(
        to_ribbon_trader_data("C"), 0xff00ff, -2.5))
    .translateX(-10)
scene.add(group)
//
// const group2 = new Group()
//     .add(drawStaircase(ribbon_data_1['Series A'], 0x00ffff, 2.5))
//     .add(drawStaircase(ribbon_data_1['Series B'], 0xffff00, 0))
//     .add(drawStaircase(ribbon_data_1['Series C'], 0xff00ff, -2.5))
//     .translateX(-10)
// //scene.add(group2)
//
// const group3 = new Group()
//     .add(drawStaircaseCylinders(ribbon_data_1['Series A'], 0x00ffff, 2.5))
//     .add(drawStaircaseCylinders(ribbon_data_1['Series B'], 0xffff00, 0))
//     .add(drawStaircaseCylinders(ribbon_data_1['Series C'], 0xff00ff, -2.5))
//     .translateX(-10)
// // scene.add(group3)
//
// const group4 = new Group()
//     .add(drawLines(ribbon_data_1['Series A'], 0x00ffff, 2.5))
//     .add(drawLines(ribbon_data_1['Series B'], 0xffff00, 0))
//     .add(drawLines(ribbon_data_1['Series C'], 0xff00ff, -2.5))
//     .translateX(-10)
// scene.add(group4)

// const group5 = new Group()
//     .add(drawExtrudedLine(ribbon_data_1['Series A'], 0x00ffff, 2.5))
//     //  .add(drawPlanesAndConnectors(ribbon_data_1['Series B'], 0xffff00, 0))
//     //  .add(drawPlanesAndConnectors(ribbon_data_1['Series C'], 0xff00ff, -2.5))
//     .translateX(-10)
//scene.add(group5)

</script>


