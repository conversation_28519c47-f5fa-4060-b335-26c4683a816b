<template>
  <canvas ref="experience"></canvas>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useThreeJSGPT4o} from "../../composables/useThreeJSGPT4o.ts.txt";
import * as THREE from 'three';

const experience = ref<HTMLCanvasElement | null>(null);
const {scene, camera} = useThreeJSGPT4o(experience)
// Create a shape for the ribbon chart
const shape = new THREE.Shape();

// Define the points for the step chart
const points = [
  new THREE.Vector2(0, 0),
  new THREE.Vector2(1, 0),
  new THREE.Vector2(1, 1),
  new THREE.Vector2(2, 1),
  new THREE.Vector2(2, 2),
  new THREE.Vector2(3, 2),
  new THREE.Vector2(3, 3)
];

// Move to the first point
shape.moveTo(points[0].x, points[0].y);

// Create the step path
for (let i = 1; i < points.length; i++) {
  shape.lineTo(points[i].x, points[i - 1].y);
  shape.lineTo(points[i].x, points[i].y);
}

// Define the extrude settings
const extrudeSettings = {
  steps: 1,
  depth: 0.1,
  bevelEnabled: false
};

// Create the geometry using the shape and extrude settings
const geometry = new THREE.ExtrudeGeometry(shape);

// Create a material for the ribbon
const material = new THREE.MeshBasicMaterial({
  color: 0x00ff00,
  side: THREE.DoubleSide
});

// Create the mesh using the geometry and material
const ribbonMesh = new THREE.Mesh(geometry, material);

// Add the ribbon mesh to your scene
scene.add(ribbonMesh);

camera.position.set(0, 0, 600);


</script>


