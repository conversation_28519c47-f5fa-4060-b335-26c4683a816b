<template>
	<div class="container">
		<div class="controls">
			<h3>Camera Controls</h3>
			<button @click="confirmReset">Reset Camera View</button>
			<div class="control-group">
				<label for="currentElevation">Current Elevation Angle: {{ currentPhi }}°</label>
				<input
					id="currentElevation"
					type="range"
					:min="minPhi"
					:max="maxPhi"
					v-model.number="currentPhi"
					@input="updateCamera"
				/>
				<span class="tooltip">Adjusts the current vertical tilt of the view.</span>
			</div>

			<div class="control-group">
				<label for="currentAzimuth">Current Azimuthal Angle: {{ currentTheta }}°</label>
				<input
					id="currentAzimuth"
					type="range"
					:min="minTheta"
					:max="maxTheta"
					v-model.number="currentTheta"
					@input="updateCamera"
				/>
				<span class="tooltip">Adjusts the current horizontal rotation of the view.</span>
			</div>
		</div>

		<div ref="plotlyDiv" class="plotly-container"></div>

		<div class="camera-info">
			<p><strong>Camera Position:</strong></p>
			<ul>
				<li>X: {{ camera.x.toFixed(2) }}</li>
				<li>Y: {{ camera.y.toFixed(2) }}</li>
				<li>Z: {{ camera.z.toFixed(2) }}</li>
			</ul>
		</div>
	</div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from "vue";
import Plotly, { Layout, PlotData, PlotlyHTMLElement } from "plotly.js-dist";

// Reference to the Plotly div
const plotlyDiv = ref<HTMLElement | null>(null);
let gd: PlotlyHTMLElement | null = null;

// Constants
const TOTAL_ROUNDS = 20;
const TOTAL_TRADERS = 10;

// Trader Labels
const traders = Array.from({ length: TOTAL_TRADERS }, (_, i) => `T${i + 1}`);

// Reactive variables for camera rotation limits
const minPhi = ref(-20);
const maxPhi = ref(60);
const minTheta = ref(-120);
const maxTheta = ref(120);

// Reactive variables for current rotation angles
const initialEye = { x: 1.5, y: 1.5, z: 1 };
const currentPhi = ref(
	Math.asin(initialEye.z / Math.sqrt(initialEye.x ** 2 + initialEye.y ** 2 + initialEye.z ** 2)) * (180 / Math.PI)
);
const currentTheta = ref(Math.atan2(initialEye.y, initialEye.x) * (180 / Math.PI));

// Reactive variable for camera position
const camera = ref({ x: initialEye.x, y: initialEye.y, z: initialEye.z });

// Timer reference for animation
const timer = ref<ReturnType<typeof setInterval> | null>(null);

// Data variables
type Data = Array<{
	round_number: number;
	round_data: Array<{ volume: number; trader: string }>;
}>;

const data = ref<Data>([
	{
		round_number: 0,
		round_data: traders.map((trader) => ({ volume: 50, trader })),
	},
	{
		round_number: 1,
		round_data: traders.map((trader) => ({ volume: 45, trader })),
	},
]);

// Initialize traces for each trader
const traces = ref<Partial<PlotData>[]>(traders.map((trader, index) => ({
	x: data.value.map(d => d.round_number),
	y: data.value.map(() => index + 1),
	z: data.value.map(d => d.round_data[index].volume),
	mode: 'lines',
	type: 'scatter3d',
	name: trader,
	line: {
		width: 5,
		color: `hsl(${(index / TOTAL_TRADERS) * 360}, 70%, 50%)`,
	},
})));

// Function to update camera based on currentPhi and currentTheta
const updateCamera = () => {
	if (!gd) return;

	// Ensure currentPhi and currentTheta are within limits
	currentPhi.value = Math.max(minPhi.value, Math.min(maxPhi.value, currentPhi.value));
	currentTheta.value = Math.max(minTheta.value, Math.min(maxTheta.value, currentTheta.value));

	const r = Math.sqrt(initialEye.x ** 2 + initialEye.y ** 2 + initialEye.z ** 2);
	const phiRad = (currentPhi.value * Math.PI) / 180;
	const thetaRad = (currentTheta.value * Math.PI) / 180;

	const newEye = {
		x: r * Math.cos(phiRad) * Math.cos(thetaRad),
		y: r * Math.cos(phiRad) * Math.sin(thetaRad),
		z: r * Math.sin(phiRad),
	};

	// Update camera position in reactive variable
	camera.value = { ...newEye };

	Plotly.relayout(gd, {
		"scene.camera.eye": newEye,
		"scene.camera.transition": {
			duration: 500,
			easing: "cubic-in-out",
		},
	});
};

// Function to reset camera to initial position and limits
const resetCamera = () => {
	if (!gd) return;

	// Reset rotation limits to initial values
	minPhi.value = -20;
	maxPhi.value = 60;
	minTheta.value = -120;
	maxTheta.value = 120;

	// Reset current rotation angles
	currentPhi.value =
		Math.asin(initialEye.z / Math.sqrt(initialEye.x ** 2 + initialEye.y ** 2 + initialEye.z ** 2)) * (180 / Math.PI);
	currentTheta.value = Math.atan2(initialEye.y, initialEye.x) * (180 / Math.PI);

	// Reset camera position
	camera.value = { ...initialEye };

	Plotly.relayout(gd, {
		"scene.camera.eye": initialEye,
		"scene.camera.transition": {
			duration: 500,
			easing: "cubic-in-out",
		},
	});
};

// Function to confirm reset action
const confirmReset = () => {
	if (confirm("Are you sure you want to reset the camera view and rotation limits to default?")) {
		resetCamera();
	}
};

// Function to initialize the Plotly plot
const initPlot = () => {
	if (!plotlyDiv.value) return;

	const layout: Partial<Layout> = {
		title: "Trader Volumes Across Rounds",
		scene: {
			xaxis: {
				title: "Rounds",
				range: [0, TOTAL_ROUNDS],
				dtick: 1,
				tick0: 0,
			},
			yaxis: {
				title: "Traders",
				type: "linear",
				tickvals: Array.from({ length: TOTAL_TRADERS }, (_, i) => i + 1),
				ticktext: traders,
				automargin: true,
			},
			zaxis: {
				title: "MMlb",
				range: [0, 50],
				tickmode: "linear",
				dtick: 5,
				tick0: 0,
			},
			camera: {
				eye: initialEye,
				up: { x: 0, y: 0, z: 1 },
			},
			dragmode: false, // Disable user rotation
		},
		autosize: true,
		showlegend: true,
	};

	Plotly.newPlot(plotlyDiv.value, traces.value, layout).then((plotlyInstance) => {
		gd = plotlyInstance;
	});
};

// Function to add a new round of data
const addNewRound = () => {
	if (!gd) return;

	const nextRoundNumber = data.value.length;
	if (nextRoundNumber >= TOTAL_ROUNDS) {
		// Stop the animation if maximum rounds reached
		if (timer.value) {
			clearInterval(timer.value);
			timer.value = null;
		}
		return;
	}

	// Decrement each trader's volume by random 0-5, not below 0
	const newRoundData = traders.map((trader) => ({
		volume: Math.max(
			data.value[data.value.length - 1].round_data.find((d) => d.trader === trader)?.volume! -
			Math.floor(Math.random() * 6),
			0
		),
		trader,
	}));

	// Add new round to data
	data.value.push({
		round_number: nextRoundNumber,
		round_data: newRoundData,
	});

	// Update traces
	newRoundData.forEach((d, traderIndex) => {
		traces.value[traderIndex].x = data.value.map((rd) => rd.round_number);
		traces.value[traderIndex].z = data.value.map((rd) => rd.round_data[traderIndex].volume);
	});

	// Update Plotly plot
	Plotly.react(
		gd,
		traces.value,
		{
			...gd.layout, // Preserve existing layout
			"scene.xaxis.range": [0, TOTAL_ROUNDS],
			"scene.yaxis.tickvals": Array.from({ length: TOTAL_TRADERS }, (_, i) => i + 1),
			"scene.yaxis.ticktext": traders,
			"scene.zaxis.range": [0, 50],
		}
	);
};

// Watchers to ensure current angles are within limits
watch([minPhi, maxPhi, minTheta, maxTheta], () => {
	// Update camera if current angles are out of new limits
	currentPhi.value = Math.max(minPhi.value, Math.min(maxPhi.value, currentPhi.value));
	currentTheta.value = Math.max(minTheta.value, Math.min(maxTheta.value, currentTheta.value));
	updateCamera();
});

// Watchers to update camera position reactively
watch([currentPhi, currentTheta], () => {
	updateCamera();
});

// Start the animation timer
const startAnimation = () => {
	timer.value = setInterval(addNewRound, 1000); // Add a new round every second
};

// Initialize plot and start animation on mount
onMounted(() => {
	initPlot();
	startAnimation();
});

// Cleanup on unmount
onUnmounted(() => {
	if (gd) {
		Plotly.purge(gd);
	}
	if (timer.value) {
		clearInterval(timer.value);
	}
});
</script>

<style scoped>
.container {
	display: flex;
	flex-direction: row;
	align-items: flex-start;
	gap: 20px;
}

.controls {
	max-width: 300px;
	padding: 15px;
	border: 1px solid #ddd;
	border-radius: 5px;
	background-color: #f9f9f9;
}

.control-group {
	margin-bottom: 20px;
}

label {
	display: block;
	margin-bottom: 5px;
	font-weight: bold;
}

input[type="range"] {
	width: 100%;
}

h3 {
	margin-bottom: 15px;
	color: #333;
}

.tooltip {
	display: block;
	margin-top: 5px;
	font-size: 12px;
	color: #666;
}

.camera-info {
	margin-top: 20px;
	padding: 10px;
	border: 1px solid #ddd;
	border-radius: 5px;
	background-color: #eef;
}

.camera-info p {
	margin: 0 0 5px 0;
	font-weight: bold;
}

.camera-info ul {
	list-style: none;
	padding: 0;
	margin: 0;
}

.camera-info li {
	margin-bottom: 3px;
}

.plotly-container {
	width: 800px;
	height: 600px;
}
</style>
