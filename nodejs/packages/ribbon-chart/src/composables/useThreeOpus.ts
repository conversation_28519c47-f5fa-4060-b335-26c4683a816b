import {shallowRef, onMounted, onUnmounted, Ref, reactive} from 'vue';
import * as THREE from 'three';
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls.js';

export function useThreeOpus(canvas: Ref<HTMLCanvasElement | null>, useOrthoCamera: boolean = false) {
    const renderer = shallowRef<THREE.WebGLRenderer | null>(null);
    const scene = new THREE.Scene();
    const camera = useOrthoCamera
        ? new THREE.OrthographicCamera(
            window.innerWidth / -2,
            window.innerWidth / 2,
            window.innerHeight / 2,
            window.innerHeight / -2,
            0.1, 1000) :
        new THREE.PerspectiveCamera(30, window.innerWidth / window.innerHeight, 0.1, 1000);
    const orbitControls = shallowRef<OrbitControls | null>(null);
    const animateFunctions = reactive<(() => void)[]>([]);

    camera.position.set(0, 10, 0);
    camera.lookAt(0, 0, 0);
    camera.zoom = useOrthoCamera ? 30 : 2; // Adjusted zoom based on camera type

    function resizeRenderer() {
        if (renderer.value) {
            const width = window.innerWidth;
            const height = window.innerHeight;
            renderer.value.setSize(width, height);
            if (useOrthoCamera) {
                (camera as THREE.OrthographicCamera).left = width / -2;
                (camera as THREE.OrthographicCamera).right = width / 2;
                (camera as THREE.OrthographicCamera).top = height / 2;
                (camera as THREE.OrthographicCamera).bottom = height / -2;
            } else {
                (camera as THREE.PerspectiveCamera).aspect = width / height;
            }
            camera.updateProjectionMatrix();
        }
    }

    function animate() {
        if (renderer.value) {
            requestAnimationFrame(animate);
            animateFunctions.forEach(func => func()); // Execute each registered function
            if (orbitControls.value) {
                orbitControls.value.update();
            }
            renderer.value.render(scene, camera);
        }
    }

    function addAnimateFunction(func: () => void) {
        animateFunctions.push(func);
    }

    function removeAnimateFunction(func: () => void) {
        const index = animateFunctions.indexOf(func);
        if (index !== -1) {
            animateFunctions.splice(index, 1);
        }
    }

    onMounted(() => {
        if (canvas.value) {
            renderer.value = new THREE.WebGLRenderer({
                canvas: canvas.value,
                antialias: true
            });
            const controls = new OrbitControls(camera, renderer.value.domElement);

            // LEFT / RIGHT;
            controls.minAzimuthAngle = -Math.PI / 8;
            controls.maxAzimuthAngle = Math.PI / 2;

            // UP / DOWN:
            controls.minPolarAngle = 0;
            controls.maxPolarAngle = (Math.PI / 2) + (Math.PI / 12);

            orbitControls.value = controls

            resizeRenderer();
            window.addEventListener('resize', resizeRenderer);
            animate();
        }
    });

    onUnmounted(() => {
        window.removeEventListener('resize', resizeRenderer);
    });

    return {
        scene,
        camera,
        orbitControls: () => orbitControls.value,
        addAnimateFunction,
        renderer: () => renderer.value,
        removeAnimateFunction
    };
}
