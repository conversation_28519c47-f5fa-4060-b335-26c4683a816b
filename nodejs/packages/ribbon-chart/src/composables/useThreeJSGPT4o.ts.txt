import {
    Color,
    PerspectiveCamera,
    Scene,
    WebGLRenderer
} from 'three';
import {computed, onMounted, ref, Ref, UnwrapRef, watch} from 'vue';
import {useWindowSize} from '@vueuse/core';
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls.js';
import {Pane} from "tweakpane";
import * as EssentialsPlugin from '@tweakpane/plugin-essentials'
import GameStats from "gamestats.js";

export function useThreeJSGPT4o(canvasRef: Ref<UnwrapRef<HTMLCanvasElement | null>>) {
    const gameStats = new GameStats();
    document.body.appendChild(gameStats.dom);

    let renderer: WebGLRenderer | null = null;
    let orbitControls: OrbitControls | null = null;

    const scene = new Scene();
    scene.background = new Color(0x000000); // Set background color to black

    const camera = new PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 5;
    scene.add(camera);

    // X Axis: Red
    // Y Axis: Green
    // Z Axis: Blue
    // const axesHelper = new AxesHelper(10);
    // scene.add(axesHelper);

    const pane = new Pane();
    pane.registerPlugin(EssentialsPlugin)
    const fpsGraph =
        pane.addBlade({
            view: 'fpsgraph',
            label: 'fpsgraph'
        });


    const {width, height} = useWindowSize();
    const aspectRatio = computed(() => width.value / height.value);

    function updateRenderer() {
        if (renderer) {
            renderer.setSize(width.value, height.value);
            renderer.setPixelRatio(window.devicePixelRatio);
        }
    }

    function updateCamera() {
        if (camera) {
            camera.aspect = aspectRatio.value;
            camera.updateProjectionMatrix();
        }
    }

    watch(aspectRatio, () => {
        updateCamera();
        updateRenderer();
    });

    const loop = () => {
        if (renderer && camera) {
            fpsGraph.begin();

          //  stats.begin();
            renderer.render(scene, camera);
          //  stats.end();

            orbitControls?.update();
            fpsGraph.end();
            requestAnimationFrame(loop);
            gameStats.update()
        }
    };

    onMounted(() => {
        renderer = new WebGLRenderer({
            canvas: canvasRef.value as HTMLCanvasElement,
            antialias: true
        });
        orbitControls = new OrbitControls(camera, renderer.domElement);
        orbitControls.enableDamping = true;

        updateCamera();
        updateRenderer();
        loop();
    });

    return {
        scene,
        camera,
        renderer,
        orbitControls,
      //  axesHelper
    }
}
