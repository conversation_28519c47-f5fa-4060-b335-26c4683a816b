import {Auction, Order} from "./auction-model";
import {RibbonPoint} from "./ribbon-series";

export const trader_rounds = (a: Auction, traderId: string): Order[] =>
    a.rounds.map(r => r.traderOrders
            .find(o => o.traderId === traderId)
        || {traderId: traderId, sellVolume: 0, buyVolume: 0})

export function to_ribbon_data(a: Auction, traderId: string):
    { [key: string]: RibbonPoint[] } {
    const ribbon_points = trader_rounds(a, traderId)
        .map((o: Order, index) => ({
            x: index + 1,
            y: o.buyVolume - o.sellVolume
        }))
    return {[traderId]: ribbon_points}
}

export const mockAuction: Auction = {
    traders: [
        {traderId: 'A'},
        {traderId: 'B'},
        {traderId: 'C'},
        {traderId: 'D'},
        {traderId: 'E'},
    ],
    rounds: [
        {
            roundNumber: 1,
            price: 2.5,
            traderOrders: [
                {traderId: 'A', buyVolume: 50, sellVolume: 0},
                {traderId: 'B', buyVolume: 0, sellVolume: 50},
                {traderId: 'C', buyVolume: 50, sellVolume: 0},
                {traderId: 'D', buyVolume: 0, sellVolume: 50},
                {traderId: 'E', buyVolume: 50, sellVolume: 0},
            ],
            totalBuyVolume: 150,
            totalSellVolume: 100,
        },
        {
            roundNumber: 2,
            price: 2.7,
            traderOrders: [
                {traderId: 'A', buyVolume: 40, sellVolume: 0},
                {traderId: 'B', buyVolume: 0, sellVolume: 50},
                {traderId: 'C', buyVolume: 50, sellVolume: 0},
                {traderId: 'D', buyVolume: 0, sellVolume: 50},
                {traderId: 'E', buyVolume: 50, sellVolume: 0},
            ],
            totalBuyVolume: 140,
            totalSellVolume: 100,
        },
        {
            roundNumber: 3,
            price: 2.9,
            traderOrders: [
                {traderId: 'A', buyVolume: 0, sellVolume: 30},
                {traderId: 'B', buyVolume: 0, sellVolume: 50},
                {traderId: 'C', buyVolume: 50, sellVolume: 0},
                {traderId: 'D', buyVolume: 0, sellVolume: 50},
                {traderId: 'E', buyVolume: 50, sellVolume: 0},
            ],
            totalBuyVolume: 100,
            totalSellVolume: 130,
        },
        {
            roundNumber: 4,
            price: 3.1,
            traderOrders: [
                {traderId: 'A', buyVolume: 0, sellVolume: 30},
                {traderId: 'B', buyVolume: 0, sellVolume: 50},
                {traderId: 'C', buyVolume: 40, sellVolume: 0},
                {traderId: 'D', buyVolume: 0, sellVolume: 50},
                {traderId: 'E', buyVolume: 40, sellVolume: 0},
            ],
            totalBuyVolume: 80,
            totalSellVolume: 130,
        },
        {
            roundNumber: 5,
            price: 3.0,
            traderOrders: [
                {traderId: 'A', buyVolume: 0, sellVolume: 30},
                {traderId: 'B', buyVolume: 0, sellVolume: 50},
                {traderId: 'C', buyVolume: 45, sellVolume: 0},
                {traderId: 'D', buyVolume: 0, sellVolume: 50},
                {traderId: 'E', buyVolume: 45, sellVolume: 0},
            ],
            totalBuyVolume: 90,
            totalSellVolume: 130,
        },
    ],
    finalPrice: 3.0,
    finalVolume: 90,
};
