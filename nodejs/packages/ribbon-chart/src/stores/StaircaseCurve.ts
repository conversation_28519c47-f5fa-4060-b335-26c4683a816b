import {Curve, Vector3} from "three";

export class StaircaseCurve extends Curve<Vector3> {

    numSteps: number
    stepSize: number

    constructor(numSteps: number, stepSize: number) {
        super();
        this.numSteps = numSteps;
        this.stepSize = stepSize;
    }

    getPoint(t:number, optionalTarget:Vector3 = new Vector3()):Vector3 {
        const step = Math.floor(t * this.numSteps);
        const tx = step * this.stepSize;
        const ty = step * this.stepSize;
        const tz = t * this.numSteps * this.stepSize;
        return optionalTarget.set(tx, ty, tz);
    }
}
