export interface Order {
    // OrderId: string;
    // roundNumber: number;
    traderId:string;
    buyVolume: number;
    sellVolume: number;
}

export interface Round {
    roundNumber: number;
    price: number;
    traderOrders: Order[];
    totalBuyVolume: number;
    totalSellVolume: number;
}

export interface Trader{
    traderId:string
}

export interface Auction {
    rounds: Round[];
    traders: Trader[];
    finalPrice: number;
    finalVolume: number;
}
