import {RibbonPoint} from "./ribbon-series";
import {Color, ColorRepresentation, Group} from "three";
import * as THREE from "three";

export function drawSpheresAndConnectors(data: RibbonPoint[],
                                  color: ColorRepresentation,
                                  z = 0): Group {

    const line_color = new Color(color).lerp(new Color('black'), 0.6)
    const group = new Group()
    const sphereRadius = 0.15;
    const cylinderRadius = 0.05;
    const sphereGeometry = new THREE.SphereGeometry(sphereRadius, 16, 16);
    const sphereMaterial = new THREE.MeshBasicMaterial({color: color});
    const cylinderMaterial = new THREE.MeshBasicMaterial({color: line_color});

    // const sphereMaterial = new THREE.MeshStandardMaterial({
    //     color, // Gold color
    //     metalness: 0.9, // High metalness value
    //     roughness: 0.2, // Low roughness for a smooth, shiny surface
    //     envMapIntensity: 1, // Enable environment map reflections
    // });
    // const cylinderMaterial = new THREE.MeshStandardMaterial({
    //     color, // Gold color
    //     metalness: 0.9, // High metalness value
    //     roughness: 0.2, // Low roughness for a smooth, shiny surface
    //     envMapIntensity: 1, // Enable environment map reflections
    // });
    //
    data.forEach((point: RibbonPoint, index: number) => {
        const sphere = new THREE.Mesh(sphereGeometry, sphereMaterial);
        sphere.position.set(point.x, point.y, z);
        group.add(sphere);

        if (index < data.length - 1) {
            const nextPoint = data[index + 1];
            const start = new THREE.Vector3(point.x, point.y, z);
            const end = new THREE.Vector3(nextPoint.x, nextPoint.y, z);
            const distance = start.distanceTo(end);
            const orientation = new THREE.Vector3().subVectors(end, start).normalize();

            const cylinderGeometry = new THREE.CylinderGeometry(cylinderRadius, cylinderRadius, distance, 8, 1);
            const cylinder = new THREE.Mesh(cylinderGeometry, cylinderMaterial);
            cylinder.position.copy(start).add(orientation.clone().multiplyScalar(distance / 2));
            cylinder.lookAt(end);
            cylinder.rotateX(Math.PI / 2);
            group.add(cylinder);
        }
    });
    return group.translateX(-5).translateY(5)
}

export function drawSteppingStones(data: RibbonPoint[],
                            color: ColorRepresentation,
                            z = 0,
                            stepWidth = 0.5,
                            stepHeight = 0.2,
                            stepDepth = 0.5): Group {

    const group = new Group();
    const stepGeometry = new THREE.BoxGeometry(stepWidth, stepHeight, stepDepth);
    const stepMaterial = new THREE.MeshBasicMaterial({color: color});

    data.forEach((point: RibbonPoint, index: number) => {
        const step = new THREE.Mesh(stepGeometry, stepMaterial);
        step.position.set(point.x, point.y + stepHeight / 2, z);
        group.add(step);

        if (index < data.length - 1) {
            const nextPoint = data[index + 1];
            const riserGeometry = new THREE.BoxGeometry(stepWidth, nextPoint.y - point.y, stepDepth);
            const riser = new THREE.Mesh(riserGeometry, stepMaterial);
            riser.position.set(point.x, (point.y + nextPoint.y) / 2, z);
            group.add(riser);
        }
    });
    return group.translateX(-5).translateY(5);
}

export function drawStaircase1(data: RibbonPoint[],
                        color: ColorRepresentation,
                        z = 0,
                        segmentWidth = 0.2,
                        segmentHeight = 0.05): Group {

    const group = new Group();
    const horizontalGeometry = new THREE.BoxGeometry(1, segmentHeight, segmentWidth);
    const verticalGeometry = new THREE.BoxGeometry(segmentWidth, 1, segmentWidth);
    const material = new THREE.MeshBasicMaterial({color: color});

    data.forEach((point: RibbonPoint, index: number) => {
        if (index < data.length - 1) {
            const nextPoint = data[index + 1];

            // Create horizontal segment
            const horizontalLength = nextPoint.x - point.x;
            const horizontal = new THREE.Mesh(horizontalGeometry.clone().scale(horizontalLength, 1, 1), material);
            horizontal.position.set((point.x + nextPoint.x) / 2, point.y, z);
            group.add(horizontal);

            // Create vertical segment
            const verticalLength = nextPoint.y - point.y;
            const vertical = new THREE.Mesh(verticalGeometry.clone().scale(1, verticalLength, 1), material);
            vertical.position.set(nextPoint.x, (point.y + nextPoint.y) / 2, z);
            group.add(vertical);
        }
    });
    return group.translateX(-5).translateY(5);
}

export function drawStaircase(data: RibbonPoint[],
                       color: ColorRepresentation,
                       z = 0,
                       segmentWidth = 0.2): Group {

    const group = new Group();
    const horizontalGeometry = new THREE.PlaneGeometry(1, segmentWidth);
    const verticalGeometry = new THREE.PlaneGeometry(segmentWidth, 1);
    const material = new THREE.MeshBasicMaterial({
        color: color,
        side: THREE.DoubleSide
    });

    data.forEach((point: RibbonPoint, index: number) => {
        if (index < data.length - 1) {
            const nextPoint = data[index + 1];

            // Create horizontal segment
            const horizontalLength = nextPoint.x - point.x;
            const horizontal = new THREE.Mesh(horizontalGeometry.clone().scale(horizontalLength, 1), material);
            horizontal.position.set((point.x + nextPoint.x) / 2, point.y, z);
            horizontal.rotation.x = -Math.PI / 2;
            group.add(horizontal);

            // Create vertical segment
            const verticalLength = nextPoint.y - point.y;
            const vertical = new THREE.Mesh(verticalGeometry.clone().scale(1, verticalLength), material);
            vertical.position.set(nextPoint.x, (point.y + nextPoint.y) / 2, z);
            group.add(vertical);
        }
    });
    return group.translateX(-5).translateY(5);
}


export function drawStaircaseCylinders(data: RibbonPoint[],
                                color: ColorRepresentation,
                                z = 0,
                                segmentWidth = 0.1,
                                segmentHeight = 0.1): Group {

    const group = new Group();
    const horizontalGeometry = new THREE.CylinderGeometry(segmentHeight, segmentHeight, 1, 16);
    const verticalGeometry = new THREE.CylinderGeometry(segmentWidth, segmentWidth, 1, 16);
    const material = new THREE.MeshBasicMaterial({color: color});

    data.forEach((point: RibbonPoint, index: number) => {
        if (index < data.length - 1) {
            const nextPoint = data[index + 1];

            // Create horizontal segment
            const horizontalLength = nextPoint.x - point.x;
            const horizontal = new THREE.Mesh(horizontalGeometry.clone().scale(horizontalLength, 1, 1), material);
            horizontal.position.set((point.x + nextPoint.x) / 2, point.y, z);
            horizontal.rotateZ(Math.PI / 2);
            group.add(horizontal);

            // Create vertical segment
            const verticalLength = nextPoint.y - point.y;
            const vertical = new THREE.Mesh(verticalGeometry.clone().scale(1, verticalLength, 1), material);
            vertical.position.set(nextPoint.x, point.y + verticalLength / 2, z);
            group.add(vertical);
        }
    });
    return group.translateX(-5).translateY(5);
}

export function drawLines(data: RibbonPoint[], color: ColorRepresentation, z = 0): Group {
    const group = new Group();
    const material = new THREE.LineBasicMaterial({color});

    for (let i = 0; i < data.length - 1; i++) {
        const start = new THREE.Vector3(data[i].x, data[i].y, z);
        const end = new THREE.Vector3(data[i + 1].x, data[i + 1].y, z);

        const points = [start, end];
        const geometry = new THREE.BufferGeometry().setFromPoints(points);
        const line = new THREE.Line(geometry, material);

        group.add(line);
    }

    return group.translateX(-5).translateY(5);
}
