export interface RibbonPoint {
    x: number;
    y: number;
}

export interface RibbonData {
    [key: string]: RibbonPoint[];
}

const correct = (p: RibbonPoint): RibbonPoint => ({
    x: p.x,
    y: p.y
})

export const ribbon_data_1: RibbonData = {
    'Series A': [
        {x: 1, y: -5.0},
        {x: 2, y: -5.0},
        {x: 3, y: -5.0},
        {x: 4, y: -4.9},
        {x: 5, y: -4.6},
        {x: 6, y: -4.7},
        {x: 7, y: -4.8},
        {x: 8, y: -5.0},
        {x: 9, y: -5.0},
        {x: 10, y: -5.0},
        {x: 11, y: -4.9},
        {x: 12, y: -4.7},
        {x: 13, y: -5.0},
        {x: 14, y: -4.7},
        {x: 15, y: -4.9},
        {x: 16, y: -4.7},
        {x: 17, y: -5.0},
        {x: 18, y: -5.0},
        {x: 19, y: -4.6},
        {x: 20, y: -4.7},
        {x: 21, y: -4.7},
        {x: 22, y: -4.6},
        {x: 23, y: -4.1},
        {x: 24, y: -4.5},
        {x: 25, y: -4.7},
        {x: 26, y: -4.4},
        {x: 27, y: -4.1},
        {x: 28, y: -3.8},
        {x: 29, y: -4.0},
        {x: 30, y: -3.8}
    ].map(correct),
    'Series B': [
        {x: 1, y: -1.0},
        {x: 2, y: -0.5},
        {x: 3, y: -0.2},
        {x: 4, y: -0.8},
        {x: 5, y: -1.2},
        {x: 6, y: -1.5},
        {x: 7, y: -1.0},
        {x: 8, y: -0.5},
        {x: 9, y: -0.8},
        {x: 10, y: -1.2},
        {x: 11, y: -1.0},
        {x: 12, y: -0.961},
        {x: 13, y: -1.396},
        {x: 14, y: -1.498},
        {x: 15, y: -1.847},
        {x: 16, y: -1.723},
        {x: 17, y: -1.965},
        {x: 18, y: -1.971},
        {x: 19, y: -2.186},
        {x: 20, y: -2.415},
        {x: 21, y: -2.308},
        {x: 22, y: -2.046},
        {x: 23, y: -1.781},
        {x: 24, y: -1.704},
        {x: 25, y: -1.703},
        {x: 26, y: -1.720},
        {x: 27, y: -1.892},
        {x: 28, y: -1.818},
        {x: 29, y: -1.514},
        {x: 30, y: -1.878}

    ].map(correct),
    'Series C': [
        {x: 1, y: 5.0},
        {x: 2, y: 4.7},
        {x: 3, y: 4.6},
        {x: 4, y: 4.5},
        {x: 5, y: 4.4},
        {x: 6, y: 4.2},
        {x: 7, y: 4.1},
        {x: 8, y: 3.9},
        {x: 9, y: 3.7},
        {x: 10, y: 3.4},
        {x: 11, y: 3.3},
        {x: 12, y: 3.2},
        {x: 13, y: 3.1},
        {x: 14, y: 2.9},
        {x: 15, y: 2.8},
        {x: 16, y: 2.7},
        {x: 17, y: 2.5},
        {x: 18, y: 2.2},
        {x: 19, y: 2.1},
        {x: 20, y: 2.0},
        {x: 21, y: 1.9},
        {x: 22, y: 1.7},
        {x: 23, y: 1.4},
        {x: 24, y: 1.2},
        {x: 25, y: 1.2},
        {x: 26, y: 1.0},
        {x: 27, y: 1.0},
        {x: 28, y: 0.9},
        {x: 29, y: 0.9},
        {x: 30, y: 0.6}
    ].map(correct),
};
