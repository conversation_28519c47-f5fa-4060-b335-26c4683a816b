<script setup lang="ts">
import Chart8PlotlyGPTo1 from "./components/ribbon/Chart8PlotlyGPTo1.vue"
import Chart9PlotlyClaude from "./components/ribbon/Chart9PlotlyClaude.vue"
import Chart10PlotlyClaude from "./components/ribbon/Chart10PlotlyClaude.vue"
import Chart11GPTSimple from "./components/ribbon/Chart11GPTSimple.vue"
import Chart12GPTDisableUserControls
	from "./components/ribbon/Chart12GPTDisableUserControls.vue"
import Chart13GPTo1miniCameraRotationV2
	from "./components/ribbon/Chart13GPTo1miniCameraRotationV2.vue"
import Chart14GPTo1miniThreeJSv1
	from "./components/ribbon/Chart14GPTo1miniThreeJSv1.vue"
import Chart15GPTo1miniPlotlyAnimation
	from "./components/ribbon/Chart15GPTo1miniPlotlyAnimation.vue"
import Chart13GPTo1miniPlotlyCameraRotation
	from "./components/ribbon/Chart13GPTo1miniPlotlyCameraRotation.vue"
import Chart16GPTo1miniThreeJSv2
	from "./components/ribbon/Chart16GPTo1miniThreeJSv2.vue"
</script>

<template>
	<!--	<Suspense>-->
	<!--		<TheExperience />-->
	<!--    <ThreeJS1></ThreeJS1>-->
	<!--    <ThreeJSGP4o></ThreeJSGP4o>-->
	<!--    <AuctionChartJSStacked :mock-auction="mockAuction"></AuctionChartJSStacked>-->
	<!--      <AuctionPlotlySurface :mock-auction="mockAuction"></AuctionPlotlySurface>-->
	<!--  <div>-->
	<!--    <button>toggle</button>-->
	<!--    <ThreeJSOpusAuction v-if="auction==='OPUS'"></ThreeJSOpusAuction>-->
	<!--    <ThreeJSGP4oAuction v-if="auction==='GPT'"></ThreeJSGP4oAuction>-->
	<!--  </div>-->
	<!-- <ThreeJSOpusRibbon></ThreeJSOpusRibbon> -->
	<!-- <ThreeJSBlackboxAuction2></ThreeJSBlackboxAuction2> -->
	<!--   <ThreeJSOpusRibbonShape></ThreeJSOpusRibbonShape>-->
	<!--  <LilGuiExp></LilGuiExp>-->
	<!--  <VueDatGuiCameraControls></VueDatGuiCameraControls>-->
<!--	  <ThreeJSOpusRibbonTube></ThreeJSOpusRibbonTube>-->
	<!--  <Ribbon1></Ribbon1>-->
	<!--  <Chart2></Chart2>-->
<!--		<Chart8PlotlyGPTo1 />-->
<!--	<Chart13GPTo1miniPlotlyCameraRotation/>-->
<!--	<Chart14GPTo1miniThreeJSv1/>-->
<!--	<Chart15GPTo1miniPlotlyAnimation/>-->
	<Chart16GPTo1miniThreeJSv2/>
	<!--    </Suspense>-->
	<!--  <GridHelperTest></GridHelperTest>-->
</template>
<script lang="ts">
const auction: string = "OPUS"


</script>
