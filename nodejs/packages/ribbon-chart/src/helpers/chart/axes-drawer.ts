import * as THREE from 'three';

export function createTickMarks(
    xAxis: THREE.Mesh,
    yAxis: THREE.Mesh,
    zAxis: THREE.Mesh,
    width: number,
    height: number,
    depth: number
): THREE.Group {
    const group = new THREE.Group();

    // Function to create a tick mark cylinder
    function createTickMark(
        position: THREE.Vector3,
        rotation: THREE.Euler,
        length: number,
        radius: number,
        color: number,
        opacity: number
    ): THREE.Mesh {
        const geometry = new THREE.CylinderGeometry(radius, radius, length, 32);
        const material = new THREE.MeshBasicMaterial({ color: color, transparent: true, opacity: opacity });
        const tickMark = new THREE.Mesh(geometry, material);
        tickMark.position.copy(position);
        tickMark.rotation.copy(rotation);
        group.add(tickMark);
        return tickMark;
    }

    // X-axis tick marks (Rounds)
    const xTickLength = 0.2;
    const xTickRadius = 0.01;
    const xTickColor = 0xffffff;
    const xTickOpacity = 0.5;
    const xTickRotation = new THREE.Euler(0, 0, Math.PI / 2);

    for (let i = 1; i <= 20; i++) {
        const xTickPosition = xAxis.position.clone();
        xTickPosition.x += (i / 20) * width;
        createTickMark(xTickPosition, xTickRotation, xTickLength, xTickRadius, xTickColor, xTickOpacity);
    }

    // Y-axis tick marks (Order volume)
    const yTickLength = 0.2;
    const yTickRadius = 0.01;
    const yTickColor = 0xffffff;
    const yTickOpacity = 0.5;
    const yTickRotation = new THREE.Euler(0, 0, 0);

    for (let i = 1; i <= 5; i++) {
        const yTickPosition = yAxis.position.clone();
        yTickPosition.y += (i / 5) * height;
        createTickMark(yTickPosition, yTickRotation, yTickLength, yTickRadius, yTickColor, yTickOpacity);
    }

    // Z-axis tick marks (Traders)
    const zTickLength = 0.2;
    const zTickRadius = 0.01;
    const zTickColor = 0xffffff;
    const zTickOpacity = 0.5;
    const zTickRotation = new THREE.Euler(Math.PI / 2, 0, 0);

    for (let i = 1; i <= 20; i++) {
        const zTickPosition = zAxis.position.clone();
        zTickPosition.z += (i / 20) * depth;
        createTickMark(zTickPosition, zTickRotation, zTickLength, zTickRadius, zTickColor, zTickOpacity);
    }

    return group;
}
