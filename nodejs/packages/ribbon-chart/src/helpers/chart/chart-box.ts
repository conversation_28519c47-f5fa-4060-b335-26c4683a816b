import {Camera, GridHelper, Group, Vector3} from "three";
import * as THREE from "three";
import {useThree<PERSON>pus} from "../../composables/useThreeOpus";
import {OrbitControls} from "three/examples/jsm/controls/OrbitControls.js";
import {editSceneObject} from "@tresjs/core/utils";
import {createTickMarks} from "./axes-drawer";
import {getIndexAxis} from "chart.js/dist/core/core.config";

export const HALF_PI = Math.PI / 2

export function createChartBax(
    camera:Camera,
    orbitControls:() => OrbitControls | null,
    addAnimateFunction:(func: () => void) => void,
    width:number, height:number,  depth:number,
    show_grid:boolean=false
):Group{

    const group = new Group()

    if(show_grid) {
        group.add(new GridHelper(width, 10))
    }

    function createCylinder(
        length: number,
        axis: 'width' | 'height' | 'depth',
        position: Vector3
    ) {

        const radius = 0.075;
        const segments = 32;
        const geometry = new THREE.CylinderGeometry(radius, radius, length, segments);
        const material = new THREE.MeshBasicMaterial({color: 0x1F4365});
        const cylinder = new THREE.Mesh(geometry, material);

        if (axis === 'width') { // X axis
            cylinder.rotation.z = HALF_PI;
        } else if (axis === 'depth') { // Z axis
            cylinder.rotation.x = HALF_PI;
        } else { // height is Y which is the default for cylinders
        }

        // Set the position
        cylinder.position.set(position.x, position.y, position.z);
        group.add(cylinder)
        return cylinder
    }

    const front = depth / 2
    const back = depth / -2
    const left = width / -2
    const right = width / 2

// (1) ACROSS
    const width_front_top = createCylinder(
        width,
        'width',
        new THREE.Vector3(0, height, front));

    const width_front_bottom = createCylinder(
        width,
        'width',
        new THREE.Vector3(0, 0, front));

    const width_back_top = createCylinder(
        width,
        'width',
        new THREE.Vector3(0, height, back));

    const width_back_bottom = createCylinder(
        width,
        'width',
        new THREE.Vector3(0, 0, back));

// (2) DEPTH:
    const depth_left_top = createCylinder(
        depth,
        'depth',
        new THREE.Vector3(left, height, 0));

    const depth_left_bottom = createCylinder(
        depth,
        'depth',
        new THREE.Vector3(left, 0, 0));

    const depth_right_top = createCylinder(
        depth,
        'depth',
        new THREE.Vector3(right, height, 0));

    const depth_right_bottom = createCylinder(
        depth,
        'depth',
        new THREE.Vector3(right, 0, 0));

// (3) HEIGHT:
    const height_left_front = createCylinder(
        height,
        'height',
        new THREE.Vector3(left, height / 2, front));

    const height_left_back = createCylinder(
        height,
        'height',
        new THREE.Vector3(left, height / 2, back));

    const height_right_front = createCylinder(
        height,
        'height',
        new THREE.Vector3(right, height / 2, front));

    const height_right_back = createCylinder(
        height,
        'height',
        new THREE.Vector3(right, height / 2, back));

   group.add(createTickMarks(
       width_front_bottom,
       height_left_front,
       depth_left_bottom,
       width,
       height,
       depth
   ))

    addAnimateFunction(() => {
        const controls = orbitControls()
        if (controls) {

            // old up/down using polar angle:
            // const polarAngle = controls.getPolarAngle();
            // const is_above_horizon = polarAngle > HALF_PI
            // const is_below_horizon = polarAngle < HALF_PI
            // width_front_top.visible = is_above_horizon
            // width_front_bottom.visible = is_below_horizon
            // width_back_top.visible = is_below_horizon
            // width_back_bottom.visible = is_above_horizon

            // USE CAMERA POSITION INSTEAD:

            const cameraPosition = camera.position.clone();

            // (1) Check if width_front_top is to the right of width_front_bottom

            // Top width visibility
            const frontTopWidthPoint = width_front_top.getWorldPosition(new THREE.Vector3());
            const cameraToFrontTopWidth = new THREE.Vector3().subVectors(frontTopWidthPoint, cameraPosition);

            const backTopWidthPoint = width_back_top.getWorldPosition(new THREE.Vector3());
            const frontTopToBackTop = new THREE.Vector3().subVectors(backTopWidthPoint, frontTopWidthPoint);

            const topCrossProduct = new THREE.Vector3().crossVectors(cameraToFrontTopWidth, frontTopToBackTop);
            if (topCrossProduct.x > 0) {
                width_front_top.visible = false;
                width_back_top.visible = true;
            } else {
                width_front_top.visible = true;
                width_back_top.visible = false;
            }

            // Bottom width visibility
            const frontBottomWidthPoint = width_front_bottom.getWorldPosition(new THREE.Vector3());
            const cameraToFrontBottomWidth = new THREE.Vector3().subVectors(frontBottomWidthPoint, cameraPosition);

            const backBottomWidthPoint = width_back_bottom.getWorldPosition(new THREE.Vector3());
            const frontBottomToBackBottom = new THREE.Vector3().subVectors(backBottomWidthPoint, frontBottomWidthPoint);

            const bottomCrossProduct = new THREE.Vector3().crossVectors(cameraToFrontBottomWidth, frontBottomToBackBottom);
            if (bottomCrossProduct.x > 0) {
                width_front_bottom.visible = true;
                width_back_bottom.visible = false;
            } else {
                width_front_bottom.visible = false;
                width_back_bottom.visible = true;
            }

            // (2) Check if height_right_front is to the right of height_right_back

            // right height visibility
            const rightFrontHeightPoint = height_right_front.getWorldPosition(new THREE.Vector3());
            const cameraToRightFrontHeight = new THREE.Vector3().subVectors(rightFrontHeightPoint, cameraPosition);

            const rightBackHeightPoint = height_right_back.getWorldPosition(new THREE.Vector3());
            const rightFrontToRightBack = new THREE.Vector3().subVectors(rightBackHeightPoint, rightFrontHeightPoint);

            const rightCrossProduct = new THREE.Vector3().crossVectors(cameraToRightFrontHeight, rightFrontToRightBack);
            if (rightCrossProduct.y > 0) {
                height_right_front.visible = true;
                height_right_back.visible = false;
            } else {
                height_right_front.visible = false;
                height_right_back.visible = true;
            }

            // left height visibility
            const leftFrontHeightPoint = height_left_front.getWorldPosition(new THREE.Vector3());
            const cameraToLeftFrontHeight = new THREE.Vector3().subVectors(leftFrontHeightPoint, cameraPosition);

            const leftBackHeightPoint = height_left_back.getWorldPosition(new THREE.Vector3());
            const leftFrontToLeftBack = new THREE.Vector3().subVectors(leftBackHeightPoint, leftFrontHeightPoint);

            const leftCrossProduct = new THREE.Vector3().crossVectors(cameraToLeftFrontHeight, leftFrontToLeftBack);
            if (leftCrossProduct.y < 0) {
                height_left_front.visible = true;
                height_left_back.visible = false;
            } else {
                height_left_front.visible = false;
                height_left_back.visible = true;
            }

            // (3) DEPTHS:
            // Top right depth visibility
            depth_right_top.visible = (height_right_front.visible && width_back_top.visible) ||
                (height_right_back.visible && width_front_top.visible);

            // Bottom right depth visibility
            depth_right_bottom.visible = (height_right_front.visible && width_back_bottom.visible) ||
                (height_right_back.visible && width_front_bottom.visible);

            // Top left depth visibility
            depth_left_top.visible = (height_left_front.visible && width_back_top.visible) ||
                (height_left_back.visible && width_front_top.visible);

            // Bottom left depth visibility
            depth_left_bottom.visible = (height_left_front.visible && width_back_bottom.visible) ||
                (height_left_back.visible && width_front_bottom.visible);

            // OVERALL VISIBILITY (not sure we need to be hidding these):
            height_left_front.visible = true
            height_left_back.visible = true
            height_right_front.visible = false
            height_right_back.visible = true
            width_back_top.visible = true
            width_back_bottom.visible = true
            width_front_top.visible = false
            width_front_bottom.visible = true
            depth_left_bottom.visible = true
            depth_left_top.visible = true
            depth_right_top.visible = false
            depth_right_bottom.visible = true

        }
    })
    return group
}
