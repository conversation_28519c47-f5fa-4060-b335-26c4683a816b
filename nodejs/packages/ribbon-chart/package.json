{"name": "ribbon-chart", "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/alvarosabu/)", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview"}, "dependencies": {"@tresjs/cientos": "^4.0.2", "@tresjs/core": "^4.2.10", "@vueuse/core": "^11.0.3", "chart.js": "^4.4.4", "luxon": "^3.5.0", "plotly.js-dist": "^2.35.2", "three": "^0.168.0", "vue": "^3.5.5"}, "devDependencies": {"@cyrilf/vue-dat-gui": "^1.1.0", "@tresjs/eslint-config-vue": "^0.2.1", "@tweakpane/core": "^2.0.4", "@tweakpane/plugin-essentials": "^0.2.1", "@types/plotly.js": "^2.33.3", "@types/three": "^0.168.0", "@vitejs/plugin-vue": "^5.1.3", "gamestats.js": "^1.0.7", "tweakpane": "^4.0.4", "typescript": "^5.6.2", "vite": "^5.4.5", "vite-plugin-glsl": "^1.3.0", "vite-plugin-vue-devtools": "^7.4.5", "vue-tsc": "^2.1.6"}}