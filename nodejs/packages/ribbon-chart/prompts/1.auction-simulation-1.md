we are designing software for a natural gas storage auction with these rules:

1. Rounds: the auction proceeds in a series of rounds.
2. Price: A price, in $/Dth, is announced at the start of the first round.
3. Traders indicate the volume, in Dth, they want to buy or sell.
4. At the end of the round, if the total buy volume (B) and total sell volume (
   S), are compared.
5. If B = S then end the auction and award the volume that each trader bid.
6. If B > S then start another round at a higher price.
7. If S > B then start another round at a lower price.
8. Continue each round in the same way until B = S, or the price overshoots,
   ie: the round price reverses.
9. Traders cannot buy more at higher prices, or less at lower prices.
10. Traders cannot sell more at lower prices, or less at higher prices.
11. Traders cannot buy high and sell low, nor sell low and buy high.

Given the above rules, what would a typescript data structure look like?

Use that data structure to create a mock auction with 3 Traders and 5 rounds.
