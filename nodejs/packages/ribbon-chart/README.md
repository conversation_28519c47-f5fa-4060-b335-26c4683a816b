# ribbon-chart

Sep 13, 2024:
- added new directory 'apps/ribbon-chart/experiments'
- goal is to experiment with various ribbon chart ideas from gpt-o1
  - these are single page ideas, so this folder could be anywhere
Here is the reference to Plotly:
    [plotly](../../docs/plotly.md)

Conclusion:
- it's NOT possible with Plotly. to intercept user rotation beforehand,
- and clamping afterwards is a terrible experience.
- so... GPTo1-mini cleverly suggested sliders to move the camera!
- see: Chart12GPTDisableUserControls

May 21, 2024:
- created a threejs composable, with help from ChatGPT4o, (Opus 3 was useless).
- based on work from this video from the creator of TresJS (which isn't working too well).
  -  [How to add 3D to your Vue App using ThreeJS - YouTube](https://www.youtube.com/watch?v=1mFWG8WBif8)

Composable:
```ts
import {BoxGeometry, Mesh, MeshBasicMaterial, PerspectiveCamera, Scene, WebGLRenderer} from 'three';
import {computed, onMounted, Ref, watch} from 'vue';
import {useWindowSize} from '@vueuse/core';
import {OrbitControls} from 'three/examples/jsm/controls/OrbitControls.js';
import {Pane} from "tweakpane";
import * as EssentialsPlugin from '@tweakpane/plugin-essentials'

let pane: Pane
let fpsGraph: any

export const useTweakPaneDM = () => {
    pane = new Pane();
    pane.registerPlugin(EssentialsPlugin)
    fpsGraph = pane.addBlade({
        view: 'fpsgraph',
        label: 'fpsgraph'
    });
    return {pane, fpsGraph}
}

export function useThreeJSGPT4o(canvasRef: Ref<HTMLCanvasElement | null>) {
    let renderer: WebGLRenderer;
    let camera: PerspectiveCamera;
    let controls: OrbitControls;

    const scene = new Scene();

    camera = new PerspectiveCamera(45, window.innerWidth / window.innerHeight, 0.1, 1000);
    camera.position.z = 5;
    scene.add(camera);

    const {width, height} = useWindowSize();
    const aspectRatio = computed(() => width.value / height.value);

    function updateRenderer() {
        if (renderer) {
            renderer.setSize(width.value, height.value);
            renderer.setPixelRatio(window.devicePixelRatio);
        }
    }

    function updateCamera() {
        if (camera) {
            camera.aspect = aspectRatio.value;
            camera.updateProjectionMatrix();
        }
    }

    watch(aspectRatio, () => {
        updateCamera();
        updateRenderer();
    });

    // const cube = new Mesh(
    //     new BoxGeometry(1, 1, 1, 32, 32),
    //     new MeshBasicMaterial({color: 0x008080})
    // );
    // scene.add(cube);

    const {fpsGraph, pane} = useTweakPaneDM();
    const loop = () => {
        if (renderer && camera) {
            fpsGraph.begin();
            renderer.render(scene, camera);
            controls.update();
            fpsGraph.end();
            requestAnimationFrame(loop);
        }
    };

    onMounted(() => {
        renderer = new WebGLRenderer({
            canvas: canvasRef.value as HTMLCanvasElement,
            antialias: true
        });
        controls = new OrbitControls(camera, renderer.domElement);
        controls.enableDamping = true;

        updateCamera();
        updateRenderer();
        loop();
    });

    return {
        scene,
        camera,
        renderer,
        controls
    };
}
```

used like this:
```html
<template>
  <canvas ref="experience"></canvas>
</template>

<script setup lang="ts">
import {ref} from "vue";
import {useThreeJSGPT4o} from "../composables/useThreeJSGPT4o";
import {BoxGeometry, Mesh, MeshBasicMaterial, SphereGeometry} from "three";
const experience = ref<HTMLCanvasElement | null>(null);
const {scene, camera} = useThreeJSGPT4o(experience)

const cube = new Mesh(
    new BoxGeometry(1, 1, 1, 32, 32),
    new MeshBasicMaterial({color: 0x008080})
);
scene.add(cube);

const sphere = new Mesh(
    new SphereGeometry(1, 20, 20),
    new MeshBasicMaterial({color: 0x080808})
);

//scene.add(sphere);
camera.position.z = 5

</script>


```
