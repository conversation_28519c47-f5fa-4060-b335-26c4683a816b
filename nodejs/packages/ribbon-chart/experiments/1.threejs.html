<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Three.js Ribbon Chart</title>
	<style>
        body { margin: 0; }
        canvas { display: block; }
	</style>
</head>
<body>
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script>
	const scene = new THREE.Scene();
	const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
	const renderer = new THREE.WebGLRenderer();
	renderer.setSize(window.innerWidth, window.innerHeight);
	document.body.appendChild(renderer.domElement);

	const geometry = new THREE.Geometry();
	const material = new THREE.LineBasicMaterial({ color: 0x0000ff });

	// Create ribbon-like geometry
	for (let i = 0; i < 100; i++) {
		geometry.vertices.push(new THREE.Vector3(Math.sin(i / 10) * 10, i / 2, Math.cos(i / 10) * 10));
	}

	const line = new THREE.Line(geometry, material);
	scene.add(line);

	camera.position.z = 100;

	function animate() {
		requestAnimationFrame(animate);
		line.rotation.x += 0.01;
		line.rotation.y += 0.01;
		renderer.render(scene, camera);
	}

	animate();
</script>
</body>
</html>
