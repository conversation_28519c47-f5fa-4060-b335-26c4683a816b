<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<title>Interactive 3D Ribbon Chart</title>
	<script src='https://cdn.plot.ly/plotly-2.35.2.min.js'></script>
	<script src='https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.17/d3.min.js'></script>
	<style>
        body { font-family: Arial, sans-serif; }
        #controls { margin-bottom: 20px; }
        .control-group { margin-bottom: 10px; }
	</style>
</head>
<body>
<div id="controls">
	<div class="control-group">
		<label for="showLegend">Show Legend:</label>
		<input type="checkbox" id="showLegend">
	</div>
	<div class="control-group">
		<label for="opacity">Opacity:</label>
		<input type="range" id="opacity" min="0" max="1" step="0.1" value="1">
	</div>
	<div class="control-group">
		<label for="aspectMode">Aspect Mode:</label>
		<select id="aspectMode">
			<option value="auto">Auto</option>
			<option value="cube">Cube</option>
			<option value="data">Data</option>
			<option value="manual">Manual</option>
		</select>
	</div>
	<div class="control-group">
		<label for="xRange">X Range:</label>
		<input type="number" id="xRangeMin" placeholder="Min" value="0">
		<input type="number" id="xRangeMax" placeholder="Max" value="49">
	</div>
	<div class="control-group">
		<label for="yRange">Y Range:</label>
		<input type="number" id="yRangeMin" placeholder="Min" value="0">
		<input type="number" id="yRangeMax" placeholder="Max" value="49">
	</div>
	<div class="control-group">
		<label for="zRange">Z Range:</label>
		<input type="number" id="zRangeMin" placeholder="Min" value="0">
		<input type="number" id="zRangeMax" placeholder="Max" value="1">
	</div>
	<div class="control-group">
		<label for="turntableRotation">Turntable Rotation Limits:</label>
		<input type="number" id="rotationMin" placeholder="Min" value="-180">
		<input type="number" id="rotationMax" placeholder="Max" value="180">
	</div>
	<button id="resetCamera">Reset Camera</button>
	<button id="toggleAnimation">Start Animation</button>
</div>
<div id='myDiv'><!-- Plotly chart will be drawn inside this DIV --></div>

<script>
	let data, layout, config;
	let animationInterval;

	d3.json('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json', function(figure) {
		data = figure.data.map((trace, index) => ({
			x: trace.x,
			y: trace.y,
			z: trace.z,
			name: `Trace ${index + 1}`,
			colorscale: trace.colorscale,
			type: 'surface',
			showscale: false
		}));

		layout = {
			title: 'Ribbon Plot',
			showlegend: false,
			autosize: true,
			width: 800,
			height: 600,
			scene: {
				xaxis: {title: 'Sample #'},
				yaxis: {title: 'Wavelength'},
				zaxis: {title: 'OD'},
				aspectmode: 'auto'
			}
		};

		config = {
			responsive: true,
			displaylogo: false,
			modeBarButtonsToRemove: ['toImage', 'sendDataToCloud']
		};

		Plotly.newPlot('myDiv', data, layout, config);

		// Add event listeners to controls
		document.getElementById('showLegend').addEventListener('change', updateLayout);
		document.getElementById('opacity').addEventListener('input', updateTraces);
		document.getElementById('aspectMode').addEventListener('change', updateLayout);
		document.getElementById('xRangeMin').addEventListener('change', updateAxesRange);
		document.getElementById('xRangeMax').addEventListener('change', updateAxesRange);
		document.getElementById('yRangeMin').addEventListener('change', updateAxesRange);
		document.getElementById('yRangeMax').addEventListener('change', updateAxesRange);
		document.getElementById('zRangeMin').addEventListener('change', updateAxesRange);
		document.getElementById('zRangeMax').addEventListener('change', updateAxesRange);
		document.getElementById('rotationMin').addEventListener('change', updateRotationLimits);
		document.getElementById('rotationMax').addEventListener('change', updateRotationLimits);
		document.getElementById('resetCamera').addEventListener('click', resetCamera);
		document.getElementById('toggleAnimation').addEventListener('click', toggleAnimation);
	});

	function updateLayout() {
		const update = {
			showlegend: document.getElementById('showLegend').checked,
			'scene.aspectmode': document.getElementById('aspectMode').value
		};
		Plotly.relayout('myDiv', update);
	}

	function updateTraces() {
		const opacity = parseFloat(document.getElementById('opacity').value);
		const update = {opacity: opacity};
		Plotly.restyle('myDiv', update);
	}

	function updateAxesRange() {
		const update = {
			'scene.xaxis.range': [
				parseFloat(document.getElementById('xRangeMin').value),
				parseFloat(document.getElementById('xRangeMax').value)
			],
			'scene.yaxis.range': [
				parseFloat(document.getElementById('yRangeMin').value),
				parseFloat(document.getElementById('yRangeMax').value)
			],
			'scene.zaxis.range': [
				parseFloat(document.getElementById('zRangeMin').value),
				parseFloat(document.getElementById('zRangeMax').value)
			]
		};
		Plotly.relayout('myDiv', update);
	}

	function updateRotationLimits() {
		const min = parseFloat(document.getElementById('rotationMin').value);
		const max = parseFloat(document.getElementById('rotationMax').value);
		const update = {
			'scene.camera.eye.z': 1.25,
			'scene.dragmode': 'turntable',
			'scene.camera.projection.type': 'orthographic'
		};
		Plotly.relayout('myDiv', update);

		let currentLayout = document.getElementById('myDiv').layout;
		currentLayout.scene.camera.up = {x: 0, y: 0, z: 1};
		currentLayout.scene.camera.center = {x: 0, y: 0, z: 0};
		currentLayout.scene.camera.eye = {x: Math.cos(min * Math.PI / 180), y: Math.sin(min * Math.PI / 180), z: 1.25};

		Plotly.relayout('myDiv', currentLayout);
	}

	function resetCamera() {
		Plotly.relayout('myDiv', {
			'scene.camera': {
				eye: {x: 1.25, y: 1.25, z: 1.25},
				center: {x: 0, y: 0, z: 0},
				up: {x: 0, y: 0, z: 1}
			}
		});
	}

	function toggleAnimation() {
		const button = document.getElementById('toggleAnimation');
		if (button.textContent === 'Start Animation') {
			button.textContent = 'Stop Animation';
			startAnimation();
		} else {
			button.textContent = 'Start Animation';
			stopAnimation();
		}
	}

	function startAnimation() {
		animationInterval = setInterval(() => {
			data.forEach((trace, i) => {
				const newX = trace.x[trace.x.length - 1] + 1;
				const newY = Math.random() * 50;
				const newZ = Math.random();

				trace.x.push(newX);
				trace.y.push(newY);
				trace.z.push(newZ);
			});
			Plotly.update('myDiv',
				{x: data.map(trace => [trace.x[trace.x.length - 1]]),
					y: data.map(trace => [trace.y[trace.y.length - 1]]),
					z: data.map(trace => [trace.z[trace.z.length - 1]])},
				{}, data.map((_, i) => i));
		}, 1000);
	}

	function stopAnimation() {
		clearInterval(animationInterval);
	}
</script>
</body>
</html>
