<!DOCTYPE html>
<html>
<head>
	<meta charset="utf-8" />
	<title>Natural Gas Pipeline Flow Animation</title>
	<script src="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.js"></script>
	<link href="https://api.mapbox.com/mapbox-gl-js/v2.15.0/mapbox-gl.css" rel="stylesheet" />
	<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/0.168.0/three.webgpu.min.js"></script>
	<style>
        body { margin: 0; padding: 0; }
        #map { position: absolute; top: 0; bottom: 0; width: 100%; }
	</style>
</head>
<body>
<div id="map"></div>
<script>
	mapboxgl.accessToken = 'pk.eyJ1IjoicndzY2FyYiIsImEiOiJjbDIxMWhmMngxMXRoM2NvM2QwNmZodXA4In0.sCZFv09GxxhOS2yaSipPFA';
	const map = new mapboxgl.Map({
		container: 'map',
		style: 'mapbox://styles/mapbox/light-v10',
		center: [-98.5795, 39.8283], // Centered on the US
		zoom: 4,
		pitch: 60,
		bearing: -17.6,
		antialias: true
	});

	let scene, camera, renderer;
	const bulges = [];
	const bulgeSpeed = 10; // Adjust based on flow data

	const pipelinePath = [
		new THREE.Vector3(-100, 40, 0),
		new THREE.Vector3(-90, 45, 0),
		new THREE.Vector3(-80, 40, 0),
		// ... add more points as needed
	];

	class FlowBulge {
		constructor(path, speed, color = 0xff0000, size = 2) {
			this.path = path;
			this.speed = speed;
			this.color = color;
			this.size = size;
			this.currentSegment = 0;
			this.progress = 0;

			const geometry = new THREE.SphereGeometry(size, 8, 8);
			const material = new THREE.MeshBasicMaterial({ color: color });
			this.mesh = new THREE.Mesh(geometry, material);
			this.mesh.position.copy(this.path[0]);
			scene.add(this.mesh);
		}

		update(deltaTime) {
			if (this.currentSegment >= this.path.length - 1) return;

			const start = this.path[this.currentSegment];
			const end = this.path[this.currentSegment + 1];
			const segmentVector = new THREE.Vector3().subVectors(end, start);
			const segmentLength = segmentVector.length();
			const direction = segmentVector.clone().normalize();

			this.progress += (this.speed * deltaTime) / segmentLength;

			if (this.progress >= 1) {
				this.currentSegment++;
				this.progress = 0;
				if (this.currentSegment >= this.path.length - 1) {
					scene.remove(this.mesh);
					return;
				}
			}

			const newPosition = start.clone().add(segmentVector.clone().multiplyScalar(this.progress));
			this.mesh.position.copy(newPosition);
		}
	}

	map.on('load', () => {
		// Initialize three.js context
		scene = new THREE.Scene();
		camera = new THREE.Camera();
		renderer = new THREE.WebGLRenderer({
			canvas: map.getCanvas(),
			context: map.painter.context.gl,
			antialias: true
		});
		renderer.autoClear = false;

		// Add pipeline mesh
		const pipelineMaterial = new THREE.MeshBasicMaterial({ color: 0x808080 });
		const pipelineGeometry = new THREE.TubeGeometry(
			new THREE.CatmullRomCurve3(pipelinePath),
			64,
			1,
			8,
			false
		);
		const pipelineMesh = new THREE.Mesh(pipelineGeometry, pipelineMaterial);
		scene.add(pipelineMesh);

		// Add custom layer
		const customLayer = {
			id: 'threejs-layer',
			type: 'custom',
			renderingMode: '3d',
			onAdd: function (map, gl) {
				this.camera = new THREE.Camera();
				this.scene = scene;
				this.renderer = renderer;
			},
			render: function (gl, matrix) {
				const m = new THREE.Matrix4().fromArray(matrix);
				this.camera.projectionMatrix = m;

				this.renderer.state.reset();
				this.renderer.render(this.scene, this.camera);
				map.triggerRepaint();
			}
		};
		map.addLayer(customLayer);

		// Animation loop
		let lastTime = performance.now();
		function animate() {
			requestAnimationFrame(animate);
			const currentTime = performance.now();
			const deltaTime = (currentTime - lastTime) / 1000; // seconds
			lastTime = currentTime;

			// Create new bulges based on flow data
			// Example: Add a bulge every 5 seconds
			if (currentTime % 5000 < deltaTime * 1000) {
				const newBulge = new FlowBulge(pipelinePath, bulgeSpeed);
				bulges.push(newBulge);
			}

			// Update bulges
			bulges.forEach((bulge, index) => {
				bulge.update(deltaTime);
				if (!bulge.mesh.parent) {
					bulges.splice(index, 1);
				}
			});

			renderer.render(scene, camera);
		}
		animate();
	});
</script>
</body>
</html>
