<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<title>3D Ribbon Plot with Interactive Controls</title>
	<!-- Load plotly.js into the DOM -->
	<script src='https://cdn.plot.ly/plotly-2.35.2.min.js'></script>
	<script src='https://cdnjs.cloudflare.com/ajax/libs/d3/3.5.17/d3.min.js'></script>
	<style>
        #controls {
            margin-bottom: 20px;
        }
        #controls label {
            display: inline-block;
            width: 150px;
        }
        #controls input, #controls select {
            margin-bottom: 10px;
        }
	</style>
</head>
<body>

<div id="controls">
	<h3>Controls</h3>
	<label for="colorscale">Colorscale:</label>
	<select id="colorscale">
		<option value="Viridis">Viridis</option>
		<option value="Cividis">Cividis</option>
		<option value="Hot">Hot</option>
		<option value="Electric">Electric</option>
		<option value="Earth">Earth</option>
	</select>
	<br>
	<label for="opacity">Opacity:</label>
	<input type="range" id="opacity" min="0" max="1" step="0.1" value="1">
	<span id="opacityValue">1</span>
	<br>
	<label for="ambient">Ambient Lighting:</label>
	<input type="range" id="ambient" min="0" max="1" step="0.1" value="0.5">
	<span id="ambientValue">0.5</span>
	<br>
	<label for="diffuse">Diffuse Lighting:</label>
	<input type="range" id="diffuse" min="0" max="1" step="0.1" value="0.5">
	<span id="diffuseValue">0.5</span>
	<br>
	<label for="rotationLimit">Rotation Limit (°):</label>
	<input type="range" id="rotationLimit" min="0" max="360" step="10" value="360">
	<span id="rotationLimitValue">360</span>
	<br><br>
	<button id="resetCamera">Reset Camera View</button>
</div>

<div id='myDiv'><!-- Plotly chart will be drawn inside this DIV --></div>

<script>
	// Define global variables
	var data = [];
	var layout = {};
	var animationInterval;
	var figureData;
	var rotating = false; // For rotation limit
	var rotationLimit = 360; // Initial rotation limit

	// Load the data
	d3.json('https://raw.githubusercontent.com/plotly/datasets/master/3d-ribbon.json', function(figure){
		figureData = figure.data; // Save initial data

		// Initialize the plot
		initPlot();

		// Set up event listeners
		setupControls();

		// Start the animation
		startAnimation();
	});

	function initPlot() {
		data = [];

		for (var i = 0; i < 7; i++) {
			var trace = {
				x: figureData[i].x,
				y: figureData[i].y,
				z: figureData[i].z,
				name: '',
				colorscale: document.getElementById('colorscale').value,
				opacity: parseFloat(document.getElementById('opacity').value),
				lighting: {
					ambient: parseFloat(document.getElementById('ambient').value),
					diffuse: parseFloat(document.getElementById('diffuse').value),
					specular: 0.1,
					roughness: 0.9,
					fresnel: 0.2
				},
				type: 'surface',
				showscale: false
			};
			data.push(trace);
		}

		layout = {
			title: 'Ribbon Plot',
			showlegend: false,
			autosize: true,
			width: 800,
			height: 600,
			scene: {
				xaxis: {title: 'Sample #'},
				yaxis: {title: 'Wavelength'},
				zaxis: {title: 'OD'},
				camera: {
					eye: {x: 1.5, y: 1.5, z: 1},
					up: {x: 0, y: 0, z: 1}
				},
				dragmode: 'turntable'
			}
		};

		Plotly.newPlot('myDiv', data, layout);

		// Set up the rotation limit event listener once
		setUpRotationLimit();
	}

	function setupControls() {
		// Update labels when sliders are moved
		document.getElementById('opacity').addEventListener('input', function() {
			document.getElementById('opacityValue').innerText = this.value;
			updatePlot();
		});

		document.getElementById('ambient').addEventListener('input', function() {
			document.getElementById('ambientValue').innerText = this.value;
			updatePlot();
		});

		document.getElementById('diffuse').addEventListener('input', function() {
			document.getElementById('diffuseValue').innerText = this.value;
			updatePlot();
		});

		document.getElementById('colorscale').addEventListener('change', function() {
			updatePlot();
		});

		document.getElementById('resetCamera').addEventListener('click', function() {
			Plotly.relayout('myDiv', {
				'scene.camera': {
					eye: {x: 1.5, y: 1.5, z: 1},
					up: {x: 0, y: 0, z: 1}
				}
			});
		});

		document.getElementById('rotationLimit').addEventListener('input', function() {
			rotationLimit = parseInt(this.value);
			document.getElementById('rotationLimitValue').innerText = this.value;
			// No need to call setRotationLimit again
		});
	}

	function updatePlot() {
		var update = {
			colorscale: document.getElementById('colorscale').value,
			opacity: parseFloat(document.getElementById('opacity').value),
			lighting: {
				ambient: parseFloat(document.getElementById('ambient').value),
				diffuse: parseFloat(document.getElementById('diffuse').value),
				specular: 0.1,
				roughness: 0.9,
				fresnel: 0.2
			}
		};

		for (var i = 0; i < data.length; i++) {
			Plotly.restyle('myDiv', update, [i]);
		}
	}

	function setUpRotationLimit() {
		var myDiv = document.getElementById('myDiv');

		myDiv.on('plotly_relayout', function(eventData) {
			if (eventData['scene.camera'] && !rotating) {
				rotating = true; // prevent recursive calls
				var camera = eventData['scene.camera'];
				var eye = camera.eye;
				var r = Math.sqrt(eye.x * eye.x + eye.y * eye.y);
				var theta = Math.atan2(eye.y, eye.x) * 180 / Math.PI;

				var limitAngle = rotationLimit / 2; // limit is total range
				if (theta > limitAngle) {
					theta = limitAngle;
				} else if (theta < -limitAngle) {
					theta = -limitAngle;
				}

				// Update eye.x and eye.y based on limited theta
				var newEyeX = r * Math.cos(theta * Math.PI / 180);
				var newEyeY = r * Math.sin(theta * Math.PI / 180);

				var update = {
					'scene.camera.eye.x': newEyeX,
					'scene.camera.eye.y': newEyeY
				};

				Plotly.relayout('myDiv', update).then(function() {
					rotating = false;
				});
			}
		});
	}

	function startAnimation() {
		var count = 0;
		var maxIterations = 10; // Limit the number of animation steps
		animationInterval = setInterval(function() {
			// For each trace, modify z values
			for (var i = 0; i < data.length; i++) {
				var newZ = [];
				for (var j = 0; j < data[i].z.length; j++) {
					var row = [];
					for (var k = 0; k < data[i].z[j].length; k++) {
						row.push(data[i].z[j][k] + 0.1); // Add 0.1 to each z value
					}
					newZ.push(row);
				}
				data[i].z = newZ;

				var update = {
					z: [data[i].z]
				};

				Plotly.restyle('myDiv', update, [i]);
			}
			count++;
			if (count >= maxIterations) {
				clearInterval(animationInterval);
			}
		}, 1000);
	}
</script>

</body>
</html>
